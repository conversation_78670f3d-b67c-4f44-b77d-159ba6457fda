using UnityEngine;

[CreateAssetMenu(fileName = "New Armor", menuName = "Equipment/Armor")]
public class Armor : EquipmentBase
{
    public int ArmorValue;
    [Tooltip("How much extra max energy this armor provides")]
    public float MaxEnergyBonus = 0f;
    public BoonType Boon; // Optional
    public int Durability;

    [System.NonSerialized]
    [Tooltip("Energy stored in this armor piece (runtime only, not saved in ScriptableObject)")]
    public float StoredEnergyContribution = 0f;

    public override void Equip(EquipmentManager equipmentManager)
    {
        // Implement armor equip logic
        var playerStatus = equipmentManager.GetComponent<PlayerStatus>();
        if (playerStatus != null)
        {
            // Tell the player status to recalculate max energy from armor
            playerStatus.RecalculateMaxEnergy();
        }
    }

    // Method to store energy when unequipping
    public void StoreCurrentEnergyContribution(float playerCurrentEnergy, float playerMaxEnergyWithoutThisArmor)
    {
        // Calculate how much energy this armor was contributing
        float energyFromThisArmor = Mathf.Max(0f, playerCurrentEnergy - playerMaxEnergyWithoutThisArmor);
        StoredEnergyContribution = Mathf.Min(energyFromThisArmor, MaxEnergyBonus);

        Debug.Log($"Armor {itemName} storing {StoredEnergyContribution} energy (from {energyFromThisArmor} calculated)");
    }

    private void OnValidate()
    {
        Slot = EquipmentSlotType.ChestSlot;
    }
}