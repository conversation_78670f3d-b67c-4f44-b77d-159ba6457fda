using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI; // This namespace is important

public class DeathManager : MonoBehaviour
{
    [<PERSON><PERSON>("References")]
    [Tooltip("Reference to the player's character controller")]
    public KinematicCharacterController.FPS.FPSCharacterController characterController;
    
    [<PERSON>lt<PERSON>("Reference to the player's camera")]
    public Camera playerCamera;
    
    [Tooltip("Reference to the player's status component")]
    public PlayerStatus playerStatus;
    
    [<PERSON><PERSON>("Death Screen Settings")]
    [Tooltip("How long to stay on black screen")]
    public float blackScreenDuration = 0.5f;

    [<PERSON><PERSON>("Revival Settings")]
    [Tooltip("Chance to revive at death location vs respawn at wake point (0-1)")]
    [Range(0f, 1f)]
    public float localRevivalChance = 0.5f;
    
    [Tooltip("Extra delay when reviving locally (for dramatic effect)")]
    public float localRevivalExtraDelay = 1f;
    
    [Tooltip("Minimum distance from wake point to allow local revival")]
    public float minDistanceForLocalRevival = 30f;

    [<PERSON><PERSON>("Respawn Settings")]

    [<PERSON><PERSON>("Death Prompt Settings")]
    [<PERSON><PERSON><PERSON>("Delay before showing the 'Press Space' prompt.")]
    public float promptDelay = 3f;

    [Tooltip("How long the main death message stays on screen before fading.")]
    public float textDisplayDuration = 2.5f;
    
    [Header("Animation Settings")]
    [Tooltip("Speed of the pulsating effect for the press space button")]
    public float pulseSpeed = 2f;
    
    [Tooltip("Intensity of the pulsating effect (0-1)")]
    [Range(0f, 1f)]
    public float pulseIntensity = 0.3f;
    
    [Tooltip("Duration of fade-in animation for text")]
    public float fadeInDuration = 1f;
    
    [Header("Debug")]
    [Tooltip("When enabled, shows debug info about wake up point selection")]
    public bool showDebugInfo = false;
    
    private List<WakeUpPoint> wakeUpPoints = new List<WakeUpPoint>();
    private bool isProcessingDeath = false;
    private bool isDead = false;
    private bool isInitialized = false;
    private Vector3 lastDeathPosition;
    private Quaternion lastDeathRotation;
    private int deathCount = 0;

    private GameObject blackScreen;
    private Image blackScreenImage;
    
    private ItemBreakageSystem itemBreakageSystem;
    
    private Text deathMessageText;
    private Text itemBreakageText;

    private Text pressSpacePromptText;
    private float timeSinceDeath = 0f;
    private bool promptIsVisible = false;
    
    // Animation variables
    private Color originalPromptColor;
    private Color originalDeathMessageColor;
    private Color originalItemBreakageColor;
    private bool promptFadeStarted = false;
    
    public static DeathManager Instance { get; private set; }
    
    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        
        Instance = this;
        CreateBlackScreen();
        itemBreakageSystem = FindObjectOfType<ItemBreakageSystem>();
        if (itemBreakageSystem == null)
        {
            GameObject breakageObj = new GameObject("ItemBreakageSystem");
            itemBreakageSystem = breakageObj.AddComponent<ItemBreakageSystem>();
            Debug.Log("DeathManager: Created ItemBreakageSystem");
        }
    }

    private void CreateBlackScreen()
    {
        GameObject canvasObj = new GameObject("DeathCanvas");
        Canvas canvas = canvasObj.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 999;

        CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);

        GameObject imageObj = new GameObject("BlackScreenImage");
        imageObj.transform.SetParent(canvasObj.transform, false);
        blackScreenImage = imageObj.AddComponent<Image>();
        blackScreenImage.color = new Color(0, 0, 0, 0);

        RectTransform rect = imageObj.GetComponent<RectTransform>();
        rect.anchorMin = Vector2.zero;
        rect.anchorMax = Vector2.one;
        rect.sizeDelta = Vector2.zero;
        rect.anchoredPosition = Vector2.zero;

        GameObject messageObj = new GameObject("DeathMessage");
        messageObj.transform.SetParent(canvasObj.transform, false);
        deathMessageText = messageObj.AddComponent<Text>();
        deathMessageText.font = Resources.Load<Font>("InstrumentSerif-Regular");
        deathMessageText.fontSize = 36;
        deathMessageText.color = Color.white;
        deathMessageText.alignment = TextAnchor.MiddleCenter;
        deathMessageText.text = "";
        originalDeathMessageColor = deathMessageText.color;
        
        RectTransform messageRect = messageObj.GetComponent<RectTransform>();
        messageRect.anchorMin = new Vector2(0.5f, 0.6f);
        messageRect.anchorMax = new Vector2(0.5f, 0.6f);
        messageRect.sizeDelta = new Vector2(800, 100);
        messageRect.anchoredPosition = Vector2.zero;
        
        GameObject breakageObj = new GameObject("ItemBreakageMessage");
        breakageObj.transform.SetParent(canvasObj.transform, false);
        itemBreakageText = breakageObj.AddComponent<Text>();
        itemBreakageText.font = Resources.Load<Font>("InstrumentSerif-Regular");
        itemBreakageText.fontSize = 24;
        itemBreakageText.color = new Color(1f, 0.7f, 0.3f);
        itemBreakageText.alignment = TextAnchor.MiddleCenter;
        itemBreakageText.text = "";
        originalItemBreakageColor = itemBreakageText.color;
        
        RectTransform breakageRect = breakageObj.GetComponent<RectTransform>();
        breakageRect.anchorMin = new Vector2(0.5f, 0.4f);
        breakageRect.anchorMax = new Vector2(0.5f, 0.4f);
        breakageRect.sizeDelta = new Vector2(600, 300);
        breakageRect.anchoredPosition = Vector2.zero;

        GameObject promptObj = new GameObject("PressSpacePrompt");
        promptObj.transform.SetParent(canvasObj.transform, false);
        pressSpacePromptText = promptObj.AddComponent<Text>();
        pressSpacePromptText.font = Resources.Load<Font>("InstrumentSerif-Regular");
        pressSpacePromptText.fontSize = 28;
        pressSpacePromptText.color = new Color(0.7f, 0.7f, 0.7f, 1f); 
        pressSpacePromptText.alignment = TextAnchor.MiddleCenter;
        pressSpacePromptText.text = "";
        originalPromptColor = pressSpacePromptText.color;

        RectTransform promptRect = promptObj.GetComponent<RectTransform>();
        promptRect.anchorMin = new Vector2(0.5f, 0.15f);
        promptRect.anchorMax = new Vector2(0.5f, 0.15f);
        promptRect.sizeDelta = new Vector2(400, 60);
        promptRect.anchoredPosition = Vector2.zero;

        // FIXED: Explicitly use UnityEngine.UI.Outline to avoid ambiguity.
        UnityEngine.UI.Outline outline = promptObj.AddComponent<UnityEngine.UI.Outline>();
        outline.effectColor = new Color(0.1f, 0.1f, 0.1f, 0.8f);
        outline.effectDistance = new Vector2(2, -2);

        blackScreen = canvasObj;
    }
    
    private void Start()
    {
        if (playerStatus == null) playerStatus = FindObjectOfType<PlayerStatus>();
        if (characterController == null && playerStatus != null)
        {
            characterController = playerStatus.GetComponent<KinematicCharacterController.FPS.FPSCharacterController>();
        }
        if (playerCamera == null) playerCamera = Camera.main;
        
        RefreshWakeUpPoints();
        isInitialized = true;
    }

    private void Update()
    {
        if (isDead && !isProcessingDeath)
        {
            timeSinceDeath += Time.deltaTime;

            if (!promptIsVisible && timeSinceDeath > promptDelay)
            {
                promptIsVisible = true;
                promptFadeStarted = true;
                StartCoroutine(FadeInText(pressSpacePromptText, "Press [SPACE]", originalPromptColor));
            }

            // Smooth pulsating effect for press space button (only after fade-in is complete)
            if (promptIsVisible && timeSinceDeath > promptDelay + fadeInDuration)
            {
                float pulse = (Mathf.Sin(Time.time * pulseSpeed) + 1f) * 0.5f; // Normalize to 0-1
                float minAlpha = originalPromptColor.a * (1f - pulseIntensity);
                float maxAlpha = originalPromptColor.a;
                float alpha = Mathf.Lerp(minAlpha, maxAlpha, pulse);
                pressSpacePromptText.color = new Color(originalPromptColor.r, originalPromptColor.g, originalPromptColor.b, alpha);
            }

            if (promptIsVisible && Input.GetKeyDown(KeyCode.Space))
            {
                StartCoroutine(FlashPromptAndProceed());
            }
        }
    }
    
    public void RefreshWakeUpPoints()
    {
        wakeUpPoints.Clear();
        wakeUpPoints.AddRange(FindObjectsOfType<WakeUpPoint>());
        if (showDebugInfo) Debug.Log($"DeathManager: Found {wakeUpPoints.Count} wake up points");
    }
    
    public void HandlePlayerDeath()
    {
        if (!isInitialized || isProcessingDeath) return;
            
        deathCount++;
        lastDeathPosition = playerStatus.transform.position;
        lastDeathRotation = playerStatus.transform.rotation;
        isDead = true;

        timeSinceDeath = 0f;
        promptIsVisible = false;
        promptFadeStarted = false;

        blackScreenImage.color = new Color(0, 0, 0, 1);
        deathMessageText.text = "";
        itemBreakageText.text = "";
        pressSpacePromptText.text = "";
    }

    private IEnumerator FlashPromptAndProceed()
    {
        isProcessingDeath = true; 
        pressSpacePromptText.color = Color.white; 
        yield return new WaitForSeconds(0.1f);
        pressSpacePromptText.text = ""; 
        
        StartCoroutine(DeathSequence());
    }

    private IEnumerator DeathSequence()
    {
        if (characterController != null) characterController.enabled = false;
        if (itemBreakageSystem != null) itemBreakageSystem.TriggerItemBreaking();
        yield return new WaitForSeconds(blackScreenDuration);
        
        bool shouldReviveLocally = DetermineLocalRevival();
        
        if (shouldReviveLocally)
        {
            yield return StartCoroutine(LocalRevivalSequence());
        }
        else
        {
            yield return StartCoroutine(WakePointRespawnSequence());
        }

        yield return new WaitForSeconds(textDisplayDuration);

        // Energy is no longer reset on death - player keeps their current energy level
        yield return StartCoroutine(FadeOutBlackScreen());
        if (characterController != null) characterController.enabled = true;
        
        isDead = false;
        isProcessingDeath = false;
    }
    
    private bool DetermineLocalRevival()
    {
        if (wakeUpPoints.Count == 0)
        {
            if (showDebugInfo) Debug.Log("DeathManager: No wake points found, forcing local revival");
            return true;
        }
        
        float nearestDistance = float.MaxValue;
        foreach (var wakePoint in wakeUpPoints)
        {
            float distance = Vector3.Distance(lastDeathPosition, wakePoint.transform.position);
            if (distance < nearestDistance) nearestDistance = distance;
        }
        
        if (nearestDistance < minDistanceForLocalRevival)
        {
            if (showDebugInfo) Debug.Log($"DeathManager: Too close to wake point ({nearestDistance}m), using wake point");
            return false;
        }
        
        bool localRevival = Random.value < localRevivalChance;
        if (showDebugInfo) Debug.Log($"DeathManager: Local revival roll: {(localRevival ? "SUCCESS" : "FAILED")} (chance: {localRevivalChance:P})");
        
        return localRevival;
    }
    
    private IEnumerator LocalRevivalSequence()
    {
        // Fade in the death message
        StartCoroutine(FadeInText(deathMessageText, "You slowly regain consciousness...", originalDeathMessageColor));
        
        if (itemBreakageSystem != null)
        {
            var brokenItems = itemBreakageSystem.GetLastBrokenItems();
            if (brokenItems.Count > 0)
            {
                string breakageMessage = "Your items took damage:\n";
                foreach (var (original, broken) in brokenItems)
                {
                    breakageMessage += $"{original.itemName} → {broken.itemName}\n";
                }
                // Fade in the item breakage message with a slight delay
                yield return new WaitForSeconds(0.5f);
                StartCoroutine(FadeInText(itemBreakageText, breakageMessage, originalItemBreakageColor));
            }
        }
        
        yield return new WaitForSeconds(localRevivalExtraDelay);
        if (characterController != null)
        {
            characterController.Motor.SetPositionAndRotation(lastDeathPosition, lastDeathRotation);
            characterController.Motor.BaseVelocity = Vector3.zero;
        }
        if (showDebugInfo) Debug.Log($"DeathManager: Player revived at death location: {lastDeathPosition}");
    }
    
    private IEnumerator WakePointRespawnSequence()
    {
        // Fade in the death message
        StartCoroutine(FadeInText(deathMessageText, "You wake up somewhere else...", originalDeathMessageColor));
        
        if (itemBreakageSystem != null)
        {
            var brokenItems = itemBreakageSystem.GetLastBrokenItems();
            if (brokenItems.Count > 0)
            {
                string breakageMessage = "Some of your items broke:\n";
                foreach (var (original, broken) in brokenItems)
                {
                    breakageMessage += $"{original.itemName} → {broken.itemName}\n";
                }
                // Fade in the item breakage message with a slight delay
                yield return new WaitForSeconds(0.5f);
                StartCoroutine(FadeInText(itemBreakageText, breakageMessage, originalItemBreakageColor));
            }
        }
        
        yield return new WaitForSeconds(0.5f);
        
        WakeUpPoint selectedPoint = SelectWakeUpPoint();
        if (selectedPoint != null)
        {
            TeleportPlayer(selectedPoint);
            selectedPoint.OnRespawn(playerStatus);
        }
        else
        {
            Debug.LogWarning("DeathManager: No wake up point found, respawning at last position");
        }
    }
    
    private IEnumerator FadeInText(Text textComponent, string message, Color targetColor)
    {
        textComponent.text = message;
        textComponent.color = new Color(targetColor.r, targetColor.g, targetColor.b, 0f);
        
        float elapsedTime = 0f;
        while (elapsedTime < fadeInDuration)
        {
            elapsedTime += Time.deltaTime;
            float alpha = elapsedTime / fadeInDuration;
            textComponent.color = new Color(targetColor.r, targetColor.g, targetColor.b, alpha);
            yield return null;
        }
        
        textComponent.color = targetColor;
    }
    
    private IEnumerator FadeOutBlackScreen()
    {
        float fadeTime = 1f;
        float elapsedTime = 0f;
        
        while (elapsedTime < fadeTime)
        {
            elapsedTime += Time.deltaTime;
            float alpha = 1f - (elapsedTime / fadeTime);
            blackScreenImage.color = new Color(0, 0, 0, alpha);
            Color textColor = deathMessageText.color;
            textColor.a = alpha;
            deathMessageText.color = textColor;
            Color breakageColor = itemBreakageText.color;
            breakageColor.a = alpha;
            itemBreakageText.color = breakageColor;
            
            yield return null;
        }
        
        blackScreenImage.color = new Color(0, 0, 0, 0);
        deathMessageText.text = "";
        itemBreakageText.text = "";
    }
    
    private WakeUpPoint SelectWakeUpPoint()
    {
        if (wakeUpPoints.Count == 0) return null;
        if (wakeUpPoints.Count == 1) return wakeUpPoints[0];
        
        float totalWeight = 0f;
        float[] weights = new float[wakeUpPoints.Count];
        for (int i = 0; i < wakeUpPoints.Count; i++)
        {
            weights[i] = wakeUpPoints[i].CalculateSelectionWeight(lastDeathPosition);
            totalWeight += weights[i];
            if (showDebugInfo) Debug.Log($"Wake Up Point '{wakeUpPoints[i].name}' weight: {weights[i]}");
        }
        
        if (totalWeight <= 0f) return wakeUpPoints[Random.Range(0, wakeUpPoints.Count)];
        
        float randomValue = Random.Range(0f, totalWeight);
        float cumulativeWeight = 0f;
        
        for (int i = 0; i < wakeUpPoints.Count; i++)
        {
            cumulativeWeight += weights[i];
            if (randomValue <= cumulativeWeight)
            {
                if (showDebugInfo) Debug.Log($"Selected wake up point: {wakeUpPoints[i].name}");
                return wakeUpPoints[i];
            }
        }
        
        return wakeUpPoints[wakeUpPoints.Count - 1];
    }
    
    private void TeleportPlayer(WakeUpPoint wakeUpPoint)
    {
        if (characterController == null || wakeUpPoint == null) return;
            
        characterController.Motor.SetPositionAndRotation(
            wakeUpPoint.transform.position, 
            wakeUpPoint.transform.rotation
        );
        characterController.Motor.BaseVelocity = Vector3.zero;
        if (showDebugInfo) Debug.Log($"Teleported player to wake up point: {wakeUpPoint.name}");
    }
}