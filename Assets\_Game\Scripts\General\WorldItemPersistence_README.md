# World Item Persistence System

This system handles the persistence of items in the game world between play sessions.

## Overview

The world item persistence system tracks items placed in the level and dropped by the player. It ensures that:

1. Items picked up by the player don't reappear when the game is reloaded
2. Items dropped by the player remain where they were dropped
3. Items with containers (like bags) maintain their contents when dropped and picked up

## Setup Requirements

To use this system, make sure you have:

1. **PersistenceManager** - Manages the save/load of world item data
2. **WorldItemManager** - Creates and manages the container for world items
3. **ItemDropPrefab** - Place this prefab in the Resources folder for dynamic item spawning
   - Alternatively, use the "DroppedEquipment" prefab if that's what you have
   - Or reference the prefab directly in the InvItemDropping component

## How It Works

1. **Item Identification**: Each item in the world gets a unique ID
   - Level-placed items get an ID based on their scene and position
   - Player-dropped items get a randomly generated unique ID
   - These IDs are stored in an ItemUniqueId component on each item

2. **Tracking Picked Up Items**: When an item is picked up
   - Its ID is added to a list of picked-up items
   - The game saves this list so the item won't be respawned on reload

3. **Tracking Dropped Items**: When a player drops an item
   - It gets a new unique ID
   - Its state is saved (position, rotation, contents if it's a container)
   - On reload, the item is spawned at the same position

4. **World Items Container**: A special container object
   - Keeps all world items organized
   - Makes it easier to manage items across scene loads

## Troubleshooting

If items aren't persisting correctly:

1. Check the console for error messages from PersistenceManager
2. Ensure your ItemDropPrefab is correctly placed in the Resources folder
3. Verify that InvItemPickup components are calling the appropriate persistence methods
4. Check that OnItemPickedUp and OnItemDropped are being called correctly

## Example Code

### Marking a manually created item for persistence

```csharp
// When creating an item in code
GameObject newItem = Instantiate(itemPrefab, position, rotation);
InvItemPickup pickup = newItem.GetComponent<InvItemPickup>();

// Register with the persistence system
PersistenceManager.Instance.RegisterWorldItem(pickup);
```

### Correctly handling player-dropped items

```csharp
// In your item dropping code
var droppedItem = Instantiate(itemPrefab, dropPosition, Quaternion.identity);
var itemPickup = droppedItem.GetComponent<InvItemPickup>();
itemPickup.SetItem(itemData, quantity);
itemPickup.MarkAsPlayerDropped(); // This registers with persistence system
``` 