using UnityEngine;
using System.Collections;
using UnityEngine.UIElements;

[RequireComponent(typeof(InvUI))]
public class MoneyCounterInitializer : MonoBehaviour
{
    // Add a debug logging flag
    private static bool verboseLogging = false;

    private void Log(string message)
    {
        if (verboseLogging || Debug.isDebugBuild)
        {
            Debug.Log(message);
        }
    }
    
    private void Awake()
    {
        // Ensure the MoneyCounterAnimation component exists
        if (GetComponent<MoneyCounterAnimation>() == null)
        {
            // Add the component if it doesn't exist
            var moneyAnimation = gameObject.AddComponent<MoneyCounterAnimation>();
            
            // Try to find suitable audio clip for the tick sound
            var audioClip = Resources.Load<AudioClip>("UI/CoinSound");
            if (audioClip != null)
            {
                // Set the field via reflection since it's private
                var field = typeof(MoneyCounterAnimation).GetField("tickSoundClip", 
                    System.Reflection.BindingFlags.NonPublic | 
                    System.Reflection.BindingFlags.Instance);
                
                if (field != null)
                {
                    field.SetValue(moneyAnimation, audioClip);
                }
            }
            
            Log("Added MoneyCounterAnimation component to InvUI GameObject");
        }
    }
    
    private void Start()
    {
        // Start initialization with a slight delay to ensure PlayerStatus is ready
        StartCoroutine(InitializeCurrencyDisplay());
    }
    
    private IEnumerator InitializeCurrencyDisplay()
    {
        // Wait a short time to ensure all components are properly initialized
        yield return new WaitForSeconds(0.5f);
        
        // Get references
        var invUI = GetComponent<InvUI>();
        var playerStatus = FindObjectOfType<PlayerStatus>();
        var moneyAnimation = GetComponent<MoneyCounterAnimation>();
        
        if (invUI != null && playerStatus != null)
        {
            // Find the best currency value from all available sources
            int bestCurrencyValue = playerStatus.Currency;
            bool needsUpdate = false;
            
            // Check direct backup first as it's most reliable for cross-scene persistence
            if (PlayerPrefs.HasKey("player_currency_last"))
            {
                int lastCurrency = PlayerPrefs.GetInt("player_currency_last", 0);
                if (lastCurrency > bestCurrencyValue)
                {
                    bestCurrencyValue = lastCurrency;
                    needsUpdate = true;
                    Log($"[MoneyCounter] Found better currency in direct backup: {bestCurrencyValue}");
                }
            }
            
            // Check emergency backup
            if (PlayerPrefs.HasKey("emergency_currency_backup"))
            {
                int emergencyCurrency = PlayerPrefs.GetInt("emergency_currency_backup", 0);
                if (emergencyCurrency > bestCurrencyValue)
                {
                    bestCurrencyValue = emergencyCurrency;
                    needsUpdate = true;
                    Log($"[MoneyCounter] Found better currency in emergency backup: {bestCurrencyValue}");
                }
            }
            
            // Check if we have a PersistenceManager with saved currency data
            var progressManager = PersistenceManager.Instance;
            if (progressManager != null)
            {
                // Get the stored currency from the progression manager
                int savedCurrency = progressManager.PlayerMoney;
                Log($"[MoneyCounter] Using currency from progression manager: {savedCurrency}");
                
                if (savedCurrency > bestCurrencyValue)
                {
                    bestCurrencyValue = savedCurrency;
                    needsUpdate = true;
                }
            }
            
            // Force set the currency value on the player status if we found a better value
            if (needsUpdate)
            {
                playerStatus.ForceSetCurrency(bestCurrencyValue);
                Log($"[MoneyCounter] Currency display initialized with value: {bestCurrencyValue}");
            }
            
            // Force an UI update to display the correct currency
            invUI.UpdateUI();
            
            // Find the currency label and ensure it uses proper formatting
            var uiDocument = GetComponent<UIDocument>();
            if (uiDocument != null && moneyAnimation != null)
            {
                var root = uiDocument.rootVisualElement;
                if (root != null)
                {
                    // Try multiple possible label names
                    var currencyLabels = root.Query<Label>().Where(l => 
                        l.name == "CurrencyLabel" || 
                        l.name == "currency-value" ||
                        l.ClassListContains("header__money")).ToList();
                    
                    foreach (var label in currencyLabels)
                    {
                        if (label != null)
                        {
                            // Use SetColoredCounterValue to ensure proper formatting from the start
                            moneyAnimation.SetColoredCounterValue(label, playerStatus.Currency);
                            Log($"[MoneyCounter] Applied colored formatting to currency label: {label.name}");
                        }
                    }
                }
            }
        }
        
        // Additional pass to ensure all currency labels are properly formatted
        yield return new WaitForSeconds(0.1f);
        EnsureAllCurrencyLabelsFormatted();
    }
    
    private void EnsureAllCurrencyLabelsFormatted()
    {
        var moneyAnimation = GetComponent<MoneyCounterAnimation>();
        if (moneyAnimation == null) return;
        
        var uiDocument = GetComponent<UIDocument>();
        if (uiDocument != null && uiDocument.rootVisualElement != null)
        {
            // Find all labels that contain currency
            var allLabels = uiDocument.rootVisualElement.Query<Label>().ToList();
            foreach (var label in allLabels)
            {
                if (label != null && label.text != null && label.text.Contains("€"))
                {
                    // Skip labels that are specifically for prices or totals
                    if (label.ClassListContains("shop-item-price") || 
                        label.name == "BuyTotalValue" || 
                        label.name == "SellTotalValue")
                    {
                        continue;
                    }
                    
                    // Ensure proper formatting
                    moneyAnimation.EnsureProperFormatting(label);
                }
            }
        }
    }
}