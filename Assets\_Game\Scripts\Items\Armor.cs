using UnityEngine;

[CreateAssetMenu(fileName = "New Armor", menuName = "Equipment/Armor")]
public class Armor : EquipmentBase
{
    public int ArmorValue;
    [Tooltip("How much extra max energy this armor provides")]
    public float MaxEnergyBonus = 0f;
    public BoonType Boon; // Optional
    public int Durability;

    // Energy storage is now handled per-instance through persistence system
    // This prevents issues with multiple identical armor pieces sharing the same stored energy value
    [System.NonSerialized]
    private float storedEnergyContribution = 0f;

    public float StoredEnergyContribution
    {
        get => storedEnergyContribution;
        set => storedEnergyContribution = value;
    }

    public override void Equip(EquipmentManager equipmentManager)
    {
        // Implement armor equip logic
        var playerStatus = equipmentManager.GetComponent<PlayerStatus>();
        if (playerStatus != null)
        {
            // Tell the player status to recalculate max energy from armor
            playerStatus.RecalculateMaxEnergy();
        }
    }

    // Method to store energy when unequipping or moving armor
    public void StoreCurrentEnergyContribution(float playerCurrentEnergy, float playerMaxEnergyWithoutThisArmor)
    {
        // Calculate how much energy this armor was contributing
        float energyFromThisArmor = Mathf.Max(0f, playerCurrentEnergy - playerMaxEnergyWithoutThisArmor);
        StoredEnergyContribution = Mathf.Min(energyFromThisArmor, MaxEnergyBonus);

        Debug.Log($"Armor {itemName} storing {StoredEnergyContribution} energy (from {energyFromThisArmor} calculated)");
    }

    // Method to create a copy of this armor with the same stored energy (for inventory operations)
    public Armor CreateInstanceCopy()
    {
        var copy = Instantiate(this);
        copy.StoredEnergyContribution = this.StoredEnergyContribution;
        return copy;
    }

    // Method to clear stored energy (called after energy is restored to player)
    public void ClearStoredEnergy()
    {
        StoredEnergyContribution = 0f;
    }

    private void OnValidate()
    {
        Slot = EquipmentSlotType.ChestSlot;
    }
}