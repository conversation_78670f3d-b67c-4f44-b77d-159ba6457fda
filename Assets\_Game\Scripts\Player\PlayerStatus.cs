using UnityEngine;
using System;
using System.Collections;
using KinematicCharacterController.FPS;

public class PlayerStatus : MonoBehaviour
{
    #region Variables
    // Add a debug logging flag
    private static bool verboseLogging = false;

    private void Log(string message)
    {
        if (verboseLogging || Debug.isDebugBuild)
        {
            Debug.Log(message);
        }
    }

    private EquipmentManager equipmentManager;
    private FPSCharacterController characterController;

    public event Action<float, float> OnHealthChanged;
    public event Action<float, float> OnEnergyChanged;
    public event Action OnSprintingStarted;
    public event Action OnSprintingStopped;
    public event Action OnWeightChanged;
    public event Action<int> OnCurrencyChanged;
    public event Action<float, float> OnMaxEnergyChanged;

    [Header("Energy (Fall Protection System)")]
    [SerializeField] private float baseEnergy = 1000f; // Base energy without armor
    public float currentEnergy;
    private float armorEnergyBonus = 0f; // Additional energy from armor
    
    // Computed property for max energy
    public float maxEnergy => baseEnergy + armorEnergyBonus;
    [SerializeField] private float energyDrainRate = 20f; // Energy drained per second when sprinting

    [Header("Sprinting")]
    private bool isSprinting = false;
    private bool sprintingAttempted = false;

    [Header("Weight")]
    public float currentWeight;
    public float baseWeight = 50f;
    [SerializeField] private float totalWeightCapacityBonus = 0f;
    public float effectiveWeightCapacity;

    // Flag to track if the application is quitting
    private bool isQuitting = false;

    [Header("Currency")]
    private CurrencyManager currencyManager;



    #endregion

    #region Properties
    public float EnergyPercentage => currentEnergy / maxEnergy;
    public bool IsSprinting => isSprinting;
    public float BaseEnergy => baseEnergy;
    public int Currency
    {
        get
        {
            // Always use CurrencyManager
            if (currencyManager != null)
            {
                return currencyManager.CurrentCurrency;
            }
            return 0; // Should never happen if properly initialized
        }
    }
    #endregion

    #region Initialization
    private void Awake()
    {
        currentEnergy = maxEnergy; // Start with full energy

        // Don't initialize currency to 0 here, as it will be loaded from PersistenceManager
        Log($"PlayerStatus Awake: Initial currency: {Currency}");
    }

    private void Start()
    {
        InitializeComponents();
        UpdateWeight();
        UpdateWeightCapacityBonus();

        // Find CurrencyManager for currency operations
        currencyManager = CurrencyManager.Instance;
        if (currencyManager == null)
        {
            Debug.LogError("[PlayerStatus] CurrencyManager not found! Spawning a new one.");
            // Create a GameObject and add CurrencyManager to ensure it exists
            GameObject cmObj = new GameObject("CurrencyManager");
            currencyManager = cmObj.AddComponent<CurrencyManager>();
        }

        // Subscribe to currency change events
        currencyManager.OnCurrencyChanged += HandleCurrencyChanged;

        // Force an immediate update to ensure UI is in sync
        OnCurrencyChanged?.Invoke(currencyManager.CurrentCurrency);

        Log($"PlayerStatus Start: Currency: {Currency}");

        // Start a delayed check to verify currency was properly initialized
        StartCoroutine(VerifyCurrencyAfterDelay());
    }

    private void InitializeComponents()
    {
        equipmentManager = GetComponent<EquipmentManager>();
        characterController = GetComponent<FPSCharacterController>();

        if (characterController == null)
        {
            Debug.LogError("FPSCharacterController component not found on the player!");
        }
    }

    private IEnumerator VerifyCurrencyAfterDelay()
    {
        // Wait to give PersistenceManager time to set currency
        yield return new WaitForSeconds(0.5f);

        // Force-update currency display if needed
        if (CurrencyManager.Instance != null)
        {
            CurrencyManager.Instance.NotifyAllListeners();
        }
    }
    #endregion

    #region Update
    private void Update()
    {
        // Handle energy for sprinting
        if (sprintingAttempted)
        {
            if (currentEnergy > 0f)
            {
                DrainEnergy();
            }
            else
            {
                if (isSprinting)
                {
                    StopSprinting();
                }
            }
        }


    }
    #endregion

    #region Energy & Sprinting Management
    public void SetSprintingAttempt(bool isAttempting)
    {
        if (sprintingAttempted != isAttempting)
        {
            sprintingAttempted = isAttempting;

            if (!sprintingAttempted && isSprinting)
            {
                StopSprinting();
            }
            else if (sprintingAttempted && currentEnergy > 0f && !isSprinting)
            {
                StartSprinting();
            }
        }
    }

    private void DrainEnergy()
    {
        float energyToReduce = energyDrainRate * Time.deltaTime;
        float oldEnergy = currentEnergy;
        currentEnergy -= energyToReduce;
        currentEnergy = Mathf.Clamp(currentEnergy, 0f, maxEnergy);

        // Notify energy change
        OnEnergyChanged?.Invoke(oldEnergy, currentEnergy);

        if (currentEnergy <= 0f)
        {
            StopSprinting();
        }
    }

    private void StartSprinting()
    {
        isSprinting = true;
        OnSprintingStarted?.Invoke();
    }

    private void StopSprinting()
    {
        isSprinting = false;
        OnSprintingStopped?.Invoke();
    }
    #endregion

    #region Energy Management
    public void DrainEnergy(float energyAmount)
    {
        float oldEnergy = currentEnergy;
        currentEnergy = Mathf.Max(0f, currentEnergy - energyAmount);
        
        // Notify energy change
        OnEnergyChanged?.Invoke(oldEnergy, currentEnergy);
        
        Log($"Player energy drained by {energyAmount}. Remaining energy: {currentEnergy}/{maxEnergy}");
    }

    public void TakeDamage(float damage)
    {
        // This is for actual damage that can kill the player (not fall damage)
        // Fall damage should use DrainEnergy instead
        
        // For now, just log that actual damage was taken
        // You can implement proper health/life system here if needed
        Log($"Player took {damage} actual damage (not energy drain)");
        
        // If you want to implement actual death from other sources:
        // HandlePlayerDeath();
    }

    // Die method removed - energy system doesn't cause death
    // If you need death from other sources, implement a separate health system


    #endregion

    #region Weight Management
    public void UpdateWeight()
    {
        float totalWeight = 0f;

        if (equipmentManager != null)
        {
            foreach (var slot in equipmentManager.GetEquipmentSlots())
            {
                if (slot.equippedItem != null)
                {
                    totalWeight += slot.equippedItem.Weight;

                    // Add weight of items in storage containers
                    if (slot.storageContainer != null)
                    {
                        foreach (var stack in slot.storageContainer.GetItems())
                        {
                            if (stack != null && stack.Item != null)
                            {
                                totalWeight += stack.Item.Weight;
                            }
                        }
                    }
                }
            }
        }

        currentWeight = totalWeight;
    }

    public void UpdateWeightCapacityBonus()
    {
        float totalBonus = 0f;
        foreach (var slot in equipmentManager.GetEquipmentSlots())
        {
            if (slot.equippedItem != null)
            {
                totalBonus += slot.equippedItem.WeightCapacityBonus;
            }
        }
        effectiveWeightCapacity = baseWeight + totalBonus;
        OnWeightChanged?.Invoke();
    }


    #endregion

    #region Health & Energy Management
    public void UpdateEnergy(float newEnergy)
    {
        float oldEnergy = currentEnergy;
        currentEnergy = Mathf.Clamp(newEnergy, 0, maxEnergy);
        OnEnergyChanged?.Invoke(oldEnergy, currentEnergy);
    }

    public void UpdateCurrency(int newCurrency)
    {
        // Always use currency manager
        currencyManager.SetCurrency(newCurrency);
        // Don't need to do anything else - our HandleCurrencyChanged will be called
    }

    public void AddCurrency(int amount)
    {
        // Always use currency manager
        currencyManager.AddCurrency(amount);
    }

    public bool TrySpendCurrency(int amount)
    {
        // Always use currency manager
        return currencyManager.TrySpendCurrency(amount);
    }

    // Method to reset energy upon respawn
    public void ResetEnergy()
    {
        float oldEnergy = currentEnergy;
        currentEnergy = maxEnergy;
        OnEnergyChanged?.Invoke(oldEnergy, currentEnergy);
        // Energy is not health - removed health event
    }

    // Method to update max energy based on armor
    public void UpdateMaxEnergy(float armorBonus)
    {
        float oldMaxEnergy = maxEnergy;
        armorEnergyBonus = armorBonus;
        float newMaxEnergy = maxEnergy; // Uses the computed property

        // Keep current energy absolute value, but clamp to new max
        currentEnergy = Mathf.Min(currentEnergy, newMaxEnergy);

        // Notify about max energy change
        OnMaxEnergyChanged?.Invoke(oldMaxEnergy, newMaxEnergy);
        OnEnergyChanged?.Invoke(currentEnergy, currentEnergy); // Trigger UI update

        Log($"Max energy updated from {oldMaxEnergy} to {newMaxEnergy} (base: {baseEnergy}, armor bonus: {armorEnergyBonus})");
    }

    // Method to be called when armor is equipped/unequipped
    public void RecalculateMaxEnergy()
    {
        float totalArmorEnergyBonus = 0f;
        float totalStoredArmorEnergy = 0f;

        // Loop through all equipped armor items
        if (equipmentManager != null)
        {
            foreach (var slot in equipmentManager.GetEquipmentSlots())
            {
                if (slot.equippedItem != null && slot.equippedItem is Armor armor)
                {
                    totalArmorEnergyBonus += armor.MaxEnergyBonus;
                    totalStoredArmorEnergy += armor.StoredEnergyContribution;
                }
            }
        }

        // Store current energy before updating max
        float energyBeforeUpdate = currentEnergy;

        // Update max energy with the calculated total
        UpdateMaxEnergy(totalArmorEnergyBonus);

        // Restore stored armor energy by adding it to current energy (clamped to new max)
        if (totalStoredArmorEnergy > 0f)
        {
            currentEnergy = Mathf.Min(energyBeforeUpdate + totalStoredArmorEnergy, maxEnergy);
            OnEnergyChanged?.Invoke(energyBeforeUpdate, currentEnergy); // Trigger UI update
            Log($"Restored {totalStoredArmorEnergy} energy from armor. Current energy: {currentEnergy}/{maxEnergy}");

            // Clear the stored energy from armor pieces since it's now been applied
            foreach (var slot in equipmentManager.GetEquipmentSlots())
            {
                if (slot.equippedItem != null && slot.equippedItem is Armor armor)
                {
                    armor.ClearStoredEnergy();
                }
            }
        }
    }

    public void RestoreEnergy(float amount)
    {
        UpdateEnergy(currentEnergy + amount);
    }

    // Method to fully restore energy (for use with special items)
    public void FullyRestoreEnergy()
    {
        UpdateEnergy(maxEnergy);
    }
    #endregion

    #region Utility
    private void OnValidate()
    {
        effectiveWeightCapacity = baseWeight + totalWeightCapacityBonus;
    }

    // Simplified method to set currency
    public void ForceSetCurrency(int newValue)
    {
        // Always use currency manager
        currencyManager.SetCurrency(newValue);
        Log($"[PlayerStatus] Force-set currency to: {newValue}");
    }

    // Save currency when application quits
    private void OnApplicationQuit()
    {
        isQuitting = true;
        
        // Let CurrencyManager handle its own saving
        if (currencyManager != null)
        {
            Log($"[PlayerStatus] Application quitting with currency: {currencyManager.CurrentCurrency}");
        }
    }

    // Add extra save on destroy to catch scene transitions
    private void OnDestroy()
    {
        // Unsubscribe from currency manager events
        if (currencyManager != null)
        {
            currencyManager.OnCurrencyChanged -= HandleCurrencyChanged;
        }
    }

    // Handle currency changes from CurrencyManager
    private void HandleCurrencyChanged(int oldValue, int newValue)
    {
        // Just forward the event to our subscribers
        OnCurrencyChanged?.Invoke(newValue);
    }
    #endregion
}