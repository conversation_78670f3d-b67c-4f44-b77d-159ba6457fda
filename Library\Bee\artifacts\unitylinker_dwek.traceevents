{ "pid": 37148, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 37148, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 37148, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 37148, "tid": 1, "ts": 1754410672316924, "dur": 477167, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 37148, "tid": 1, "ts": 1754410672319061, "dur": 59512, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 37148, "tid": 1, "ts": 1754410672327642, "dur": 49106, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 37148, "tid": 1, "ts": 1754410672405929, "dur": 14777, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 37148, "tid": 1, "ts": 1754410672422213, "dur": 78780, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 37148, "tid": 1, "ts": 1754410672501038, "dur": 19555, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 37148, "tid": 1, "ts": 1754410672520610, "dur": 262894, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 37148, "tid": 1, "ts": 1754410672791164, "dur": 2740, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 37148, "tid": 1, "ts": 1754410672794093, "dur": 497, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 37148, "tid": 1, "ts": 1754410672803899, "dur": 2296, "ph": "X", "name": "", "args": {} },
{ "pid": 37148, "tid": 1, "ts": 1754410672803183, "dur": 3443, "ph": "X", "name": "Write chrome-trace events", "args": {} },
