{ "pid": 250832, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 250832, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 250832, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 250832, "tid": 1, "ts": 1754406579127338, "dur": 1670975, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 250832, "tid": 1, "ts": 1754406579129341, "dur": 323830, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 250832, "tid": 1, "ts": 1754406579394215, "dur": 57147, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 250832, "tid": 1, "ts": 1754406579500362, "dur": 12647, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 250832, "tid": 1, "ts": 1754406579514051, "dur": 132892, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 250832, "tid": 1, "ts": 1754406579646993, "dur": 928841, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 250832, "tid": 1, "ts": 1754406580575856, "dur": 206413, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 250832, "tid": 1, "ts": 1754406580794083, "dur": 4064, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 250832, "tid": 1, "ts": 1754406580798314, "dur": 435, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 250832, "tid": 1, "ts": 1754406580809830, "dur": 2696, "ph": "X", "name": "", "args": {} },
{ "pid": 250832, "tid": 1, "ts": 1754406580808101, "dur": 4857, "ph": "X", "name": "Write chrome-trace events", "args": {} },
