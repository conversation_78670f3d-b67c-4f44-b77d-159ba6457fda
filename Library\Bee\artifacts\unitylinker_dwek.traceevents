{ "pid": 262904, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 262904, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 262904, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 262904, "tid": 1, "ts": 1754409897207217, "dur": 1869127, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 262904, "tid": 1, "ts": 1754409897209721, "dur": 336524, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 262904, "tid": 1, "ts": 1754409897491850, "dur": 52172, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 262904, "tid": 1, "ts": 1754409897598854, "dur": 15125, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 262904, "tid": 1, "ts": 1754409897615321, "dur": 142672, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 262904, "tid": 1, "ts": 1754409897758041, "dur": 1102651, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 262904, "tid": 1, "ts": 1754409898860715, "dur": 205316, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 262904, "tid": 1, "ts": 1754409899073545, "dur": 2629, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 262904, "tid": 1, "ts": 1754409899076346, "dur": 470, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 262904, "tid": 1, "ts": 1754409899085786, "dur": 3135, "ph": "X", "name": "", "args": {} },
{ "pid": 262904, "tid": 1, "ts": 1754409899084825, "dur": 4635, "ph": "X", "name": "Write chrome-trace events", "args": {} },
