{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754409895929296, "dur":14266, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754409895943578, "dur":594, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754409895944223, "dur":50, "ph":"X", "name": "Tundra",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1754409895944274, "dur":582, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754409895953059, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754409895954003, "dur":346, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestAssetBundleModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754409895954381, "dur":107, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityWebRequestAssetBundleModule-FeaturesChecked.txt_5xwn.info" }}
,{ "pid":12345, "tid":0, "ts":1754409895955812, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityPlayer.dll" }}
,{ "pid":12345, "tid":0, "ts":1754409895944894, "dur":11051, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754409895955954, "dur":9447070, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754409905403027, "dur":221, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754409905403326, "dur":56, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754409905403568, "dur":3981, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754409895945084, "dur":10880, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754409895961395, "dur":432, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Assets/StreamingAssets" }}
,{ "pid":12345, "tid":1, "ts":1754409895961827, "dur":1921, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":1, "ts":1754409895963749, "dur":18320, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":1, "ts":1754409895982070, "dur":293, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport" }}
,{ "pid":12345, "tid":1, "ts":1754409895982363, "dur":4333, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono" }}
,{ "pid":12345, "tid":1, "ts":1754409895986697, "dur":237, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/Data/Resources" }}
,{ "pid":12345, "tid":1, "ts":1754409895986935, "dur":407, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1754409895987342, "dur":434, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Library/PlayerDataCache/Win642/Data" }}
,{ "pid":12345, "tid":1, "ts":1754409895987776, "dur":126, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/Plugins" }}
,{ "pid":12345, "tid":1, "ts":1754409895987902, "dur":96, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/UnitySubsystems" }}
,{ "pid":12345, "tid":1, "ts":1754409895955971, "dur":32036, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754409895988018, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/7893594715672875865.rsp" }}
,{ "pid":12345, "tid":1, "ts":1754409895988080, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754409895988506, "dur":761, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895989272, "dur":211, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895989485, "dur":180, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895989671, "dur":220, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895989897, "dur":175, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895990078, "dur":303, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895990382, "dur":558, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895990941, "dur":464, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895991407, "dur":229, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895991642, "dur":922, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895992567, "dur":268, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895992837, "dur":221, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895993060, "dur":547, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895993608, "dur":213, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895993824, "dur":207, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895994037, "dur":204, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895994247, "dur":235, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895994483, "dur":217, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895994706, "dur":286, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895994994, "dur":421, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895995417, "dur":909, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895996328, "dur":188, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895996518, "dur":148, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895996671, "dur":229, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895996907, "dur":362, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895997272, "dur":216, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409895997490, "dur":2661, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409896000158, "dur":506, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409896000666, "dur":592, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409896001259, "dur":432, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409896001693, "dur":325, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409896002020, "dur":671, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409896002693, "dur":851, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409896003545, "dur":171, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409896003717, "dur":703, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409896004815, "dur":153, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\MethodsToPreserve.xml" }}
,{ "pid":12345, "tid":1, "ts":1754409896004969, "dur":141, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\TypesInScenes.xml" }}
,{ "pid":12345, "tid":1, "ts":1754409896005112, "dur":149, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\SerializedTypes.xml" }}
,{ "pid":12345, "tid":1, "ts":1754409896005263, "dur":234, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\EditorToUnityLinkerData.json" }}
,{ "pid":12345, "tid":1, "ts":1754409895988218, "dur":17703, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker C:/Unity/BLAME/BLAME/Library/Bee/artifacts/unitylinker_dwek.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1754409896006977, "dur":144, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754409896066319, "dur":3050663, "ph":"X", "name": "UnityLinker",  "args": { "detail":"C:/Unity/BLAME/BLAME/Library/Bee/artifacts/unitylinker_dwek.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1754409899192210, "dur":366027, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409899558385, "dur":158, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409899558662, "dur":12139, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409899570935, "dur":32970, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409899604056, "dur":462770, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Mono.Security.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409900066830, "dur":13778, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\mscorlib.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409900080753, "dur":47730, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\netstandard.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409900128487, "dur":146280, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409900275072, "dur":372, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.ComponentModel.Composition.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409900275447, "dur":73856, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Configuration.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409900349305, "dur":29027, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409900378478, "dur":207351, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Drawing.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409900585845, "dur":259955, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409900845821, "dur":161913, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Xml.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409899191509, "dur":1816426, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":1, "ts":1754409901013156, "dur":4366301, "ph":"X", "name": "GenerateNativePluginsForAssemblies",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":1, "ts":1754409905381025, "dur":21787, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\AsyncPluginsFromLinker\\x86_64\\lib_burst_generated.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409905380924, "dur":21890, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Plugins/x86_64/lib_burst_generated.dll" }}
,{ "pid":12345, "tid":1, "ts":1754409905402816, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895945110, "dur":10860, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895956391, "dur":841, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\WindowsBase.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895957233, "dur":686, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.runtimeconfig.json" }}
,{ "pid":12345, "tid":2, "ts":1754409895957919, "dur":801, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.exe" }}
,{ "pid":12345, "tid":2, "ts":1754409895958720, "dur":810, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll.config" }}
,{ "pid":12345, "tid":2, "ts":1754409895959530, "dur":795, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895960325, "dur":755, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.deps.json" }}
,{ "pid":12345, "tid":2, "ts":1754409895961081, "dur":731, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.TinyProfiler.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895961813, "dur":799, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Options.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895962613, "dur":1124, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.xml" }}
,{ "pid":12345, "tid":2, "ts":1754409895963737, "dur":698, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895964435, "dur":701, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895965137, "dur":734, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Shell.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895965871, "dur":970, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Compile.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895966841, "dur":831, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common35.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895967673, "dur":757, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895968431, "dur":747, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895969178, "dur":1004, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895955981, "dur":14201, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895970183, "dur":636, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.dll.config" }}
,{ "pid":12345, "tid":2, "ts":1754409895970819, "dur":1057, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.runtimeconfig.json" }}
,{ "pid":12345, "tid":2, "ts":1754409895971876, "dur":894, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.exe" }}
,{ "pid":12345, "tid":2, "ts":1754409895973268, "dur":696, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\hostpolicy.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895970183, "dur":7087, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895978107, "dur":776, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895978883, "dur":605, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Runtime.Extensions.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895979845, "dur":625, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Resources.Writer.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895981809, "dur":538, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Reflection.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895982347, "dur":743, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Reflection.TypeExtensions.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895983531, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895984065, "dur":595, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Reflection.Emit.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895985836, "dur":607, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409895977270, "dur":9530, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895986800, "dur":1222, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895988031, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895988627, "dur":734, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895989377, "dur":752, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895990140, "dur":852, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895991016, "dur":760, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895991788, "dur":626, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895992419, "dur":678, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895993104, "dur":674, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895993785, "dur":625, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895994418, "dur":745, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895995168, "dur":758, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895995934, "dur":958, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895996900, "dur":607, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895997515, "dur":581, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895998113, "dur":385, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/web.config" }}
,{ "pid":12345, "tid":2, "ts":1754409895998499, "dur":610, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409895999116, "dur":147, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":2, "ts":1754409895999263, "dur":196, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409896018141, "dur":95831, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets.resS" }}
,{ "pid":12345, "tid":2, "ts":1754409896114090, "dur":3077494, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409899192314, "dur":105872, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityWebRequestAssetBundleModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409899191585, "dur":106603, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409899298190, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409899298438, "dur":124456, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ParticleSystemModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409899298327, "dur":124573, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ParticleSystemModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409899422901, "dur":245, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409899423294, "dur":255128, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.PropertiesModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409899423158, "dur":255306, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754409899678465, "dur":198, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409899678664, "dur":125, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754409899679175, "dur":111907, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UmbraModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409899679042, "dur":112042, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UmbraModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409899791086, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409899791348, "dur":68587, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.InputModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409899791231, "dur":68738, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.InputModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409899859970, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409899860341, "dur":246311, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.VisualScripting.Antlr3.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409899860152, "dur":246542, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.Antlr3.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754409900106696, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900107355, "dur":182, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.AIModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409900107234, "dur":304, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AIModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409900107539, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900107654, "dur":59, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AIModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409900107832, "dur":131963, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.VRModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409900107714, "dur":132083, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VRModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409900239798, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900240146, "dur":107662, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409900239935, "dur":107915, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754409900347851, "dur":179, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900348031, "dur":129, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754409900348472, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900348961, "dur":157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900349521, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900350097, "dur":180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900350665, "dur":27193, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.HotReloadModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409900350527, "dur":27371, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.HotReloadModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754409900377898, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900378403, "dur":61815, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityCurlModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409900378289, "dur":61970, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityCurlModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754409900440260, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900440758, "dur":120222, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409900440645, "dur":120336, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409900560982, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900561259, "dur":332905, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.PhysicsModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409900561119, "dur":333046, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.PhysicsModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409900894166, "dur":191, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900894387, "dur":562, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900894955, "dur":51, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.VisualScripting.Antlr3.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409900895010, "dur":223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900895257, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900895384, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900895604, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900895784, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900896256, "dur":353, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900896983, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900897364, "dur":39121, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1754409900897361, "dur":39161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.HighDefinition.Config.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754409900936523, "dur":273, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900937117, "dur":228, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900937688, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900938307, "dur":1397, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900939704, "dur":104, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.EnterpriseServices-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754409900940737, "dur":233, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Drawing-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754409900940971, "dur":1565, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754409900943100, "dur":4459920, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895945132, "dur":10849, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895955988, "dur":620, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WindowsDesktop.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895956609, "dur":860, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WebGL.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895957470, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.VisionOS.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895958149, "dur":825, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.UniversalWindows.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895958974, "dur":709, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.MacOSX.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895959683, "dur":754, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Linux.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895960437, "dur":773, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.iOS.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895961211, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.EmbeddedLinux.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895961952, "dur":954, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895962906, "dur":947, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.AppleTV.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895963853, "dur":662, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Android.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895964515, "dur":721, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.xml" }}
,{ "pid":12345, "tid":3, "ts":1754409895965236, "dur":750, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895965986, "dur":917, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895966903, "dur":749, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Cecil.Awesome.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895967652, "dur":663, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Api.Attributes.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895968315, "dur":697, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.XDocument.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895969012, "dur":1015, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895970028, "dur":710, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlSerializer.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895970738, "dur":926, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlDocument.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895955988, "dur":15676, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895971665, "dur":1284, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.Toolchain.TvOS.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895973176, "dur":523, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.Toolchain.LLVM.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895971665, "dur":5275, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895976941, "dur":508, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Security.Cryptography.Hashing.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895978285, "dur":999, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Security.Cryptography.Encryption.ECDiffieHellman.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895979758, "dur":545, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895982524, "dur":502, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895983516, "dur":509, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Runtime.Serialization.Primitives.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895984025, "dur":602, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Runtime.Serialization.Json.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409895976941, "dur":8886, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895985827, "dur":269, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895986097, "dur":1915, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895988032, "dur":777, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895988820, "dur":697, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895989523, "dur":742, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895990270, "dur":766, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895991041, "dur":882, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895991931, "dur":625, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895992564, "dur":734, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895993307, "dur":678, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895993991, "dur":754, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895994775, "dur":736, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895995519, "dur":837, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895996364, "dur":915, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895997284, "dur":662, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895997989, "dur":312, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/mconfig/config.xml" }}
,{ "pid":12345, "tid":3, "ts":1754409895998301, "dur":317, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895998642, "dur":329, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":3, "ts":1754409895998972, "dur":266, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895999245, "dur":145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":3, "ts":1754409895999391, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409895999524, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409896037266, "dur":51297, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.resource" }}
,{ "pid":12345, "tid":3, "ts":1754409896089913, "dur":18738, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0.resS" }}
,{ "pid":12345, "tid":3, "ts":1754409896108834, "dur":3082668, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409899192289, "dur":76388, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.VehiclesModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409899191503, "dur":77180, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VehiclesModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409899268684, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409899268807, "dur":137, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VehiclesModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409899323859, "dur":158890, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.CrashReportingModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409899268947, "dur":213803, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.CrashReportingModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409899482752, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409899482890, "dur":55, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.CrashReportingModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409899483159, "dur":210941, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.GameCenterModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409899482948, "dur":211195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GameCenterModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754409899694144, "dur":4860, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409899699005, "dur":60, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GameCenterModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754409899699463, "dur":108579, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.AudioModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409899699348, "dur":108695, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AudioModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409899808044, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409899808278, "dur":52291, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityConnectModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409899808164, "dur":52406, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityConnectModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409899860571, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409899860852, "dur":147545, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409899860704, "dur":147737, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Mathematics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754409900008450, "dur":253, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409900008704, "dur":54, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Mathematics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754409900009334, "dur":254462, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409900009137, "dur":254691, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Net.Http-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754409900263829, "dur":252, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409900264082, "dur":61, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Net.Http-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754409900264509, "dur":134213, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409900264375, "dur":134348, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409900398725, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409900399041, "dur":114549, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409900398890, "dur":114741, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.State-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754409900513632, "dur":157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409900514080, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409900514543, "dur":55624, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ShaderVariantAnalyticsModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409900514423, "dur":55787, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ShaderVariantAnalyticsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754409900570212, "dur":296, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409900570509, "dur":224, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ShaderVariantAnalyticsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754409900571286, "dur":28581, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityWebRequestTextureModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409900571100, "dur":28810, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestTextureModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754409900599910, "dur":164, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409900600358, "dur":185, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409900600797, "dur":1347, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409900602993, "dur":157666, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityWebRequestModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409900602153, "dur":158621, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754409900760779, "dur":272, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409900761766, "dur":126278, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.TextRenderingModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409900761547, "dur":126612, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754409900888160, "dur":435, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409900888596, "dur":55, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754409900888943, "dur":3930, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409900888941, "dur":3933, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409900892875, "dur":317, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409900893200, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409900893394, "dur":259, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409900893695, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409900894228, "dur":43539, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409900894105, "dur":43663, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409900937769, "dur":5478, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754409900943248, "dur":54, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1754409900943303, "dur":4459719, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895945154, "dur":10841, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895956002, "dur":711, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XDocument.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895956714, "dur":847, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895957562, "dur":639, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.ReaderWriter.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895958201, "dur":792, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895958993, "dur":712, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895959705, "dur":821, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Windows.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895960526, "dur":697, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.HttpUtility.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895961224, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895961932, "dur":852, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ValueTuple.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895962784, "dur":1056, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.Local.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895963840, "dur":657, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895964497, "dur":713, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Timer.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895965217, "dur":737, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.ThreadPool.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895965955, "dur":876, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Thread.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895966831, "dur":790, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Parallel.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895967621, "dur":847, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Extensions.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895968468, "dur":724, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895969192, "dur":961, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Dataflow.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895970153, "dur":1015, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Overlapped.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895971169, "dur":826, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895956002, "dur":15994, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895972282, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceProcess.dll" }}
,{ "pid":12345, "tid":4, "ts":****************, "dur":922, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.dll" }}
,{ "pid":12345, "tid":4, "ts":****************, "dur":599, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Routing.dll" }}
,{ "pid":12345, "tid":4, "ts":****************, "dur":510, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Internals.dll" }}
,{ "pid":12345, "tid":4, "ts":****************, "dur":563, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Remoting.dll" }}
,{ "pid":12345, "tid":4, "ts":****************, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Caching.dll" }}
,{ "pid":12345, "tid":4, "ts":****************, "dur":563, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reflection.Context.dll" }}
,{ "pid":12345, "tid":4, "ts":****************, "dur":630, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Providers.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895971996, "dur":10090, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895982576, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\Microsoft.Win32.Registry.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895983110, "dur":522, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\Microsoft.Win32.Registry.AccessControl.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895983632, "dur":506, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409895982086, "dur":4478, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895986564, "dur":1445, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895988029, "dur":516, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895988557, "dur":399, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895988969, "dur":744, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895989721, "dur":704, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895990434, "dur":712, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895991154, "dur":921, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895992083, "dur":736, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895992824, "dur":695, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895993527, "dur":576, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895994111, "dur":721, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895994838, "dur":567, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895995412, "dur":805, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895996222, "dur":935, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895997165, "dur":681, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895997852, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895998002, "dur":425, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/config" }}
,{ "pid":12345, "tid":4, "ts":1754409895998427, "dur":248, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895998679, "dur":330, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":4, "ts":1754409895999009, "dur":257, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409895999266, "dur":69, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":4, "ts":1754409896000781, "dur":77512, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets2.assets" }}
,{ "pid":12345, "tid":4, "ts":1754409896078691, "dur":10198, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level2" }}
,{ "pid":12345, "tid":4, "ts":1754409896089991, "dur":16677, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0" }}
,{ "pid":12345, "tid":4, "ts":1754409896108508, "dur":3082990, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409899192227, "dur":108987, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409899191500, "dur":109715, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409899301215, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409899301444, "dur":282230, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409899301334, "dur":282341, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409899583677, "dur":149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409899583976, "dur":289156, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409899583838, "dur":289296, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409899873136, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409899873437, "dur":322260, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409899873305, "dur":322434, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.TextMeshPro-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754409900195740, "dur":162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409900196176, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409900196539, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409900196769, "dur":177133, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Data.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409900196656, "dur":177288, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Data-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754409900373945, "dur":181, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409900374559, "dur":90995, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.SubstanceModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409900374417, "dur":91178, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SubstanceModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754409900465595, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409900465995, "dur":437, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409900466439, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409900466710, "dur":4667, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Burst.Unsafe.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409900466575, "dur":4803, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Burst.Unsafe.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409900471379, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409900471531, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409900471670, "dur":274, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409900472069, "dur":535823, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Xml.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409900471950, "dur":535943, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Xml.dll" }}
,{ "pid":12345, "tid":4, "ts":1754409901007894, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754409901008091, "dur":4394944, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895945186, "dur":10857, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895956051, "dur":663, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Channels.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895956715, "dur":782, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.RegularExpressions.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895957497, "dur":691, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Json.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895958188, "dur":839, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encodings.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895959027, "dur":729, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.Extensions.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895959756, "dur":790, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895960546, "dur":797, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.CodePages.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895961343, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceProcess.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895962021, "dur":990, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895963011, "dur":880, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895963891, "dur":647, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.Windows.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895964539, "dur":778, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895965317, "dur":786, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895966103, "dur":957, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.X509Certificates.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895967060, "dur":829, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Primitives.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895967890, "dur":832, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.OpenSsl.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895968722, "dur":805, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895969527, "dur":532, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895970059, "dur":1184, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Csp.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895971244, "dur":654, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Cng.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895956051, "dur":15847, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895972733, "dur":686, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.BuildTools.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895973624, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\mscorlib.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895974204, "dur":516, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\monodoc.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895974720, "dur":585, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\cscompmgd.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895976624, "dur":591, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895977216, "dur":572, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Xml.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895978683, "dur":500, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Xaml.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895971898, "dur":9496, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895982255, "dur":542, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.Utilities.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895982797, "dur":562, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895983360, "dur":559, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.ServicePoint.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895985869, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.NameResolution.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895981395, "dur":7220, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895988617, "dur":646, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895989271, "dur":755, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895990034, "dur":681, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895990724, "dur":637, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895991369, "dur":730, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895992103, "dur":626, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895992735, "dur":704, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895993445, "dur":595, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895994047, "dur":629, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895994680, "dur":614, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895995306, "dur":794, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895996114, "dur":863, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895996985, "dur":552, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895997542, "dur":616, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895998165, "dur":486, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/settings.map" }}
,{ "pid":12345, "tid":5, "ts":1754409895998651, "dur":503, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409895999157, "dur":170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409895999328, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409896001532, "dur":294750, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets.resS" }}
,{ "pid":12345, "tid":5, "ts":1754409896296400, "dur":2895147, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409899192236, "dur":107905, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.VFXModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409899191547, "dur":108631, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.VFXModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754409899300183, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409899300680, "dur":82373, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409899300563, "dur":82491, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409899383055, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409899383338, "dur":353438, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409899383204, "dur":353613, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754409899736818, "dur":176, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409899737330, "dur":71220, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ClothModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409899737217, "dur":71334, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ClothModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409899808552, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409899808836, "dur":32523, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ClusterInputModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409899808678, "dur":32682, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ClusterInputModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409899841361, "dur":256, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409899841824, "dur":57309, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409899841630, "dur":57505, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409899899136, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409899899444, "dur":44100, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409899899292, "dur":45240, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409899944535, "dur":338, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409899944889, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.DirectorModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754409899945067, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409899945563, "dur":70227, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.VirtualTexturingModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409899945449, "dur":70342, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VirtualTexturingModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409900015792, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900016032, "dur":392064, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Security.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409900015925, "dur":392214, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Security-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754409900408141, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900408582, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900408897, "dur":2478, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/Managed/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409900411379, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900411497, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Security.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409900411560, "dur":412, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900411980, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900412123, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900412234, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900412350, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900412463, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900412574, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900412681, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900412895, "dur":162358, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409900412787, "dur":162468, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409900575927, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900576097, "dur":196, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900576304, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900576436, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900576555, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900576666, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900576794, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900576915, "dur":174, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900577099, "dur":223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900577467, "dur":268296, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409900577328, "dur":268437, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409900845767, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900846044, "dur":117533, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.AccessibilityModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754409900845918, "dur":117700, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AccessibilityModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754409900963619, "dur":248, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754409900964168, "dur":4438863, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895945231, "dur":10820, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895956059, "dur":664, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Algorithms.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895956724, "dur":821, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Claims.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895957545, "dur":671, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.AccessControl.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895958216, "dur":801, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895959018, "dur":753, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Primitives.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895959771, "dur":750, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Json.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895960521, "dur":737, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Formatters.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895961259, "dur":735, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895961994, "dur":956, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Numerics.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895962951, "dur":939, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Loader.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895963890, "dur":620, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Intrinsics.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895964510, "dur":748, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.RuntimeInformation.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895965258, "dur":786, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.JavaScript.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895966045, "dur":850, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895966895, "dur":877, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895967773, "dur":749, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Extensions.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895968522, "dur":696, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895969218, "dur":1183, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895970401, "dur":948, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.Unsafe.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895971350, "dur":1044, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Writer.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895956059, "dur":16335, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895972395, "dur":537, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\SMDiagnostics.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895972933, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\RabbitMQ.Client.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895973545, "dur":691, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\PEAPI.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895975469, "dur":538, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Tasklets.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895976773, "dur":583, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Security.Win32.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895979616, "dur":647, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Messaging.RabbitMQ.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895980654, "dur":563, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Http.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895972394, "dur":9614, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895982466, "dur":547, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895983449, "dur":557, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895984006, "dur":615, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895985794, "dur":630, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895982008, "dur":6761, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895988771, "dur":697, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895989477, "dur":716, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895990202, "dur":823, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895991031, "dur":611, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895991650, "dur":691, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895992349, "dur":679, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895993035, "dur":889, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895993937, "dur":616, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895994562, "dur":707, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895995274, "dur":934, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895996214, "dur":970, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895997218, "dur":718, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895997940, "dur":207, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityPlayer.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895998147, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895998290, "dur":378, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":6, "ts":1754409895998669, "dur":513, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409895999189, "dur":311, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/D3D12/D3D12Core.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409895999501, "dur":537, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409896000781, "dur":85156, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets" }}
,{ "pid":12345, "tid":6, "ts":1754409896086124, "dur":18612, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1.resS" }}
,{ "pid":12345, "tid":6, "ts":1754409896104887, "dur":3086666, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899192242, "dur":98237, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.AMDModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409899191554, "dur":98968, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AMDModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754409899290523, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899291066, "dur":51330, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.TerrainPhysicsModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409899290931, "dur":51466, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.TerrainPhysicsModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409899342398, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899342748, "dur":59901, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ContentLoadModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409899342539, "dur":60162, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ContentLoadModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754409899402702, "dur":899, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899404085, "dur":79019, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.GridModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409899403966, "dur":79178, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GridModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754409899483145, "dur":12329, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899496626, "dur":228211, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.AndroidJNIModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409899495729, "dur":229149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754409899724880, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899725417, "dur":89887, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409899725304, "dur":90001, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409899815307, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899815633, "dur":66753, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.LocalizationModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409899815473, "dur":66915, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.LocalizationModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409899882389, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899882587, "dur":220, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899883097, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899883532, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899884608, "dur":316, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899884934, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899885078, "dur":174, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899885261, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899885385, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899885521, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899885667, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899885806, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899885972, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899886346, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899886606, "dur":73486, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Transactions.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409899886483, "dur":73610, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Transactions.dll" }}
,{ "pid":12345, "tid":6, "ts":1754409899960095, "dur":194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754409899960302, "dur":88, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Assembly-CSharp-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754409899962598, "dur":1099863, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/Features/Assembly-CSharp-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754409901066167, "dur":4336858, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409895945260, "dur":10799, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409895956066, "dur":713, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.ResourceManager.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895956780, "dur":807, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Reader.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895957587, "dur":700, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.TypeExtensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895958287, "dur":764, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Primitives.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895959051, "dur":796, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Metadata.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895959847, "dur":781, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895960629, "dur":716, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.Lightweight.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895961346, "dur":700, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.ILGeneration.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895962047, "dur":1022, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895963069, "dur":840, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895963909, "dur":685, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895964594, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.Linq.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895965286, "dur":772, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895966059, "dur":877, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Uri.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895966936, "dur":753, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.DataContractSerialization.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895967689, "dur":699, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.CoreLib.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895968388, "dur":753, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895969142, "dur":1254, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895970396, "dur":662, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895971058, "dur":755, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895956066, "dur":15747, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409895972723, "dur":634, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\BuildPipeline\\NiceIO.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895971814, "dur":3761, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409895975576, "dur":551, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\I18N.CJK.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895978286, "dur":601, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Xml.XPath.XDocument.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895979965, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Threading.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895982295, "dur":526, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Threading.Tasks.Parallel.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895982821, "dur":588, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Threading.Tasks.Extensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895983409, "dur":543, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Threading.Overlapped.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895975576, "dur":8796, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409895984373, "dur":3630, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409895988062, "dur":539, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409895988618, "dur":316, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\boot.config" }}
,{ "pid":12345, "tid":7, "ts":1754409895988942, "dur":162, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\RuntimeInitializeOnLoads.json" }}
,{ "pid":12345, "tid":7, "ts":1754409895989110, "dur":163, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":7, "ts":1754409895989375, "dur":154, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895989530, "dur":166, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895989698, "dur":214, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895989913, "dur":174, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895990089, "dur":285, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895990380, "dur":479, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895990861, "dur":543, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895991406, "dur":242, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895991649, "dur":916, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895992570, "dur":252, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895992828, "dur":215, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895993048, "dur":554, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895993604, "dur":267, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895993873, "dur":179, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895994053, "dur":198, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895994253, "dur":195, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895994455, "dur":285, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895994741, "dur":238, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895994985, "dur":517, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895995504, "dur":814, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895996321, "dur":184, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895996511, "dur":164, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895996677, "dur":617, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895997297, "dur":218, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895997517, "dur":2432, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895999952, "dur":166, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409896000124, "dur":247, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409896000373, "dur":661, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409896001036, "dur":276, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409896001319, "dur":458, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409896001784, "dur":944, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409896002729, "dur":758, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409896003490, "dur":188, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409896003680, "dur":384, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409895988607, "dur":16232, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":7, "ts":1754409896005588, "dur":116, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409896006587, "dur":2109767, "ph":"X", "name": "AddBootConfigGUID",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":7, "ts":1754409898158844, "dur":119, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\boot.config" }}
,{ "pid":12345, "tid":7, "ts":1754409898158769, "dur":195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":7, "ts":1754409898158980, "dur":1221, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":7, "ts":1754409898160203, "dur":1031302, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409899192252, "dur":58675, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.DirectorModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409899191508, "dur":59421, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.DirectorModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409899250930, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409899251234, "dur":89021, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.NVIDIAModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409899251104, "dur":89194, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.NVIDIAModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754409899340299, "dur":172, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409899340838, "dur":60814, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.MultiplayerModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409899340731, "dur":60963, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.MultiplayerModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754409899401701, "dur":1899, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409899404058, "dur":69710, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.WindModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409899403911, "dur":69899, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.WindModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754409899473811, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409899474437, "dur":347014, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UIElementsModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409899474324, "dur":347169, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754409899821494, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409899822202, "dur":254845, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409899821906, "dur":255182, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754409900077089, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900077617, "dur":126314, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.XRModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409900077504, "dur":126429, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.XRModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409900203935, "dur":191, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900204262, "dur":170334, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Core.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409900204138, "dur":170556, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Core-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754409900374695, "dur":219, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900377421, "dur":96849, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.StreamingModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409900377297, "dur":97015, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.StreamingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754409900474313, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900474786, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900474944, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900475368, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900475772, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900476180, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900476597, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900477027, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900477419, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900477809, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900478184, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ARModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754409900478269, "dur":224, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900478747, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900479221, "dur":37652, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityTestProtocolModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409900479106, "dur":37806, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityTestProtocolModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754409900516913, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900517366, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900517891, "dur":107909, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409900517771, "dur":108031, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409900625803, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900626166, "dur":91854, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.SharedInternalsModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409900625956, "dur":92106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754409900718063, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900718645, "dur":152580, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.HierarchyCoreModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409900718464, "dur":152808, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754409900871274, "dur":335, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900871958, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900872791, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900872926, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900873054, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900873230, "dur":493, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900874250, "dur":68, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Burst.Unsafe-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754409900874319, "dur":239, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900874841, "dur":13932, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.TextRenderingModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409900874838, "dur":13936, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.TextRenderingModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409900888775, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900888917, "dur":47585, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.JSONSerializeModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409900888914, "dur":47589, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.JSONSerializeModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754409900936505, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900936690, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900937164, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900937731, "dur":444, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900939718, "dur":79, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754409900939798, "dur":246, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900940734, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Recorder-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754409900940793, "dur":174, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900942513, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestAssetBundleModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754409900942570, "dur":412, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754409900943327, "dur":4459701, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895945283, "dur":10815, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895956105, "dur":698, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.Client.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895956804, "dur":834, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebProxy.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895957638, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebHeaderCollection.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895958346, "dur":785, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebClient.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895959131, "dur":768, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895959899, "dur":748, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.ServicePoint.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895960648, "dur":768, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Security.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895961416, "dur":749, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Requests.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895962165, "dur":1117, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Quic.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895963283, "dur":876, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Primitives.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895964159, "dur":677, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Ping.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895964836, "dur":705, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NetworkInformation.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895965541, "dur":970, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NameResolution.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895966511, "dur":666, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Mail.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895967178, "dur":665, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.HttpListener.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895967843, "dur":792, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.Json.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895968635, "dur":1317, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895969952, "dur":711, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895970675, "dur":947, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Memory.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895971622, "dur":922, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Queryable.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895956105, "dur":16439, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895972972, "dur":820, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Cairo.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895974634, "dur":536, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Btls.Interface.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895975570, "dur":507, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.VisualC.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895976550, "dur":555, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895978388, "dur":672, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Engine.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895979060, "dur":645, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\ICSharpCode.SharpZipLib.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895980569, "dur":504, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\I18N.West.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895972544, "dur":9602, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895984414, "dur":685, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895982147, "dur":3585, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895985733, "dur":690, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.AIModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409895985732, "dur":3107, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895988843, "dur":744, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895989595, "dur":166, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895989771, "dur":997, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895990774, "dur":751, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895991535, "dur":728, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895992272, "dur":643, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895992921, "dur":678, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895993607, "dur":613, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895994228, "dur":800, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895995036, "dur":695, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895995743, "dur":717, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895996468, "dur":880, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895997355, "dur":628, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895997988, "dur":307, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":8, "ts":1754409895998296, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895998415, "dur":275, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/settings.map" }}
,{ "pid":12345, "tid":8, "ts":1754409895998690, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409895998852, "dur":221, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/machine.config" }}
,{ "pid":12345, "tid":8, "ts":1754409895999073, "dur":562, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409896031063, "dur":50, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409896081038, "dur":9900, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity_builtin_extra" }}
,{ "pid":12345, "tid":8, "ts":1754409896091365, "dur":20339, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers" }}
,{ "pid":12345, "tid":8, "ts":1754409896111828, "dur":3079705, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409899192312, "dur":166723, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.IO.Compression.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409899191533, "dur":167543, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.IO.Compression-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754409899359078, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409899359219, "dur":110, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.IO.Compression-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754409899359715, "dur":172867, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.AssetBundleModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409899359590, "dur":173035, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AssetBundleModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754409899532627, "dur":308, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409899532936, "dur":63, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AssetBundleModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754409899535535, "dur":164280, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.SpriteMaskModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409899535420, "dur":164453, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SpriteMaskModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754409899699875, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409899700490, "dur":352756, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.IMGUIModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409899700362, "dur":352885, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.IMGUIModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900053249, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409900053382, "dur":55, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.IMGUIModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900053595, "dur":209807, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900053440, "dur":210002, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Newtonsoft.Json-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754409900263443, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409900263598, "dur":111, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Newtonsoft.Json-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754409900264057, "dur":171869, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900263935, "dur":172107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.GPUDriven.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754409900436043, "dur":501, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409900436545, "dur":50, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.GPUDriven.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754409900436964, "dur":28649, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900436858, "dur":28756, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900465615, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409900465751, "dur":370, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409900466129, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409900466287, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409900466464, "dur":87065, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900466461, "dur":87070, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900553532, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409900553659, "dur":114, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900558300, "dur":1544, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900553776, "dur":6069, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900559847, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409900560110, "dur":85570, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.TilemapModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900559991, "dur":85690, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.TilemapModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900645683, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409900646020, "dur":100242, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ClusterRendererModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900645830, "dur":100573, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClusterRendererModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754409900746405, "dur":586, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409900747426, "dur":141825, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ScreenCaptureModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900747310, "dur":142015, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ScreenCaptureModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754409900889335, "dur":291, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409900889969, "dur":141, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900889851, "dur":260, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900890111, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409900890234, "dur":183, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409900890427, "dur":946, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409900891383, "dur":73665, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.AccessibilityModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900891381, "dur":73668, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AccessibilityModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1754409900965054, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754409900965201, "dur":4437842, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895945313, "dur":10795, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895956114, "dur":721, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Parallel.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895956836, "dur":865, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Expressions.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895957701, "dur":770, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895958471, "dur":727, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.UnmanagedMemoryStream.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895959198, "dur":818, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895960016, "dur":749, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.AccessControl.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895960766, "dur":707, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895961474, "dur":723, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895962197, "dur":1135, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895963332, "dur":725, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895964057, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895964725, "dur":685, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895965410, "dur":773, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.AccessControl.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895966183, "dur":812, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895966996, "dur":779, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.ZipFile.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895967776, "dur":707, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Native.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895968483, "dur":700, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895969183, "dur":1268, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895970451, "dur":740, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Brotli.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895971191, "dur":754, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895956114, "dur":15831, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895971945, "dur":958, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Windows.Forms.DataVisualization.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895973364, "dur":822, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.WebPages.dll" }}
,{ "pid":12345, "tid":9, "ts":****************, "dur":544, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.WebPages.Deployment.dll" }}
,{ "pid":12345, "tid":9, "ts":****************, "dur":509, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Routing.dll" }}
,{ "pid":12345, "tid":9, "ts":****************, "dur":639, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Razor.dll" }}
,{ "pid":12345, "tid":9, "ts":****************, "dur":507, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Mvc.dll" }}
,{ "pid":12345, "tid":9, "ts":****************, "dur":868, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Http.dll" }}
,{ "pid":12345, "tid":9, "ts":****************, "dur":515, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Http.SelfHost.dll" }}
,{ "pid":12345, "tid":9, "ts":****************, "dur":538, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Extensions.Design.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895971945, "dur":10349, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895984854, "dur":513, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.TLSModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409895982295, "dur":4119, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895986415, "dur":1595, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895988029, "dur":534, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895988573, "dur":604, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895989190, "dur":681, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895989876, "dur":691, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895990576, "dur":833, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895991415, "dur":775, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895992198, "dur":691, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895992897, "dur":747, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895993649, "dur":642, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895994299, "dur":773, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895995078, "dur":716, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895995799, "dur":673, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895996476, "dur":826, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895997307, "dur":643, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895997951, "dur":91, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Burst.Unsafe-FeaturesChecked.txt_1w2c.info" }}
,{ "pid":12345, "tid":9, "ts":1754409895998043, "dur":434, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/browscap.ini" }}
,{ "pid":12345, "tid":9, "ts":1754409895998478, "dur":229, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895998712, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":9, "ts":1754409895998842, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895999035, "dur":301, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":9, "ts":1754409895999337, "dur":270, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409895999621, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409896043101, "dur":65814, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets.resS" }}
,{ "pid":12345, "tid":9, "ts":1754409896109042, "dur":3082458, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409899192178, "dur":111695, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.DSPGraphModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409899191502, "dur":112372, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.DSPGraphModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409899303875, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409899304194, "dur":773594, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\mscorlib.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409899304049, "dur":773771, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/mscorlib-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754409900077820, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409900078419, "dur":106609, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409900078304, "dur":106725, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409900185031, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409900185222, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Autodesk.Fbx-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754409900185289, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409900185444, "dur":72, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Autodesk.Fbx-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754409900185872, "dur":758151, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":9, "ts":1754409900185755, "dur":758301, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.ProBuilder-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754409900944056, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754409900944632, "dur":4458398, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895945361, "dur":10754, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895956122, "dur":791, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895956914, "dur":766, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895957680, "dur":754, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Tar.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895958435, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Asn1.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895959103, "dur":811, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Dynamic.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895959914, "dur":761, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895960675, "dur":757, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895961432, "dur":781, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895962214, "dur":1141, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tracing.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895963355, "dur":698, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TraceSource.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895964053, "dur":628, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tools.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895964681, "dur":686, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895965367, "dur":762, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895966129, "dur":901, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895967030, "dur":811, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895967841, "dur":753, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.DiagnosticSource.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895968594, "dur":725, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895969319, "dur":682, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895970002, "dur":790, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895970792, "dur":946, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895956122, "dur":15616, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895971739, "dur":1045, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Analytics.deps.json" }}
,{ "pid":12345, "tid":10, "ts":1754409895971739, "dur":4191, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895978654, "dur":638, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409895975930, "dur":6569, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895982499, "dur":4128, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895986628, "dur":1438, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895988071, "dur":645, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895988725, "dur":664, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895989399, "dur":755, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895990160, "dur":741, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895990909, "dur":539, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895991463, "dur":622, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895992091, "dur":673, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895992770, "dur":746, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895993524, "dur":578, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895994108, "dur":717, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895994834, "dur":571, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895995425, "dur":842, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895996272, "dur":908, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895997187, "dur":643, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895997838, "dur":484, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895998328, "dur":187, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/web.config" }}
,{ "pid":12345, "tid":10, "ts":1754409895998515, "dur":204, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895998725, "dur":211, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/settings.map" }}
,{ "pid":12345, "tid":10, "ts":1754409895998936, "dur":256, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409895999197, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME.exe" }}
,{ "pid":12345, "tid":10, "ts":1754409895999318, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409896017599, "dur":71220, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.resource" }}
,{ "pid":12345, "tid":10, "ts":1754409896089971, "dur":73, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409896090046, "dur":22638, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets.resS" }}
,{ "pid":12345, "tid":10, "ts":1754409896112812, "dur":3078696, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409899192172, "dur":77426, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409899191509, "dur":78125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/BakeryRuntimeAssembly-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754409899269635, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409899270107, "dur":44941, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409899269996, "dur":45054, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409899315052, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409899315410, "dur":102074, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.InputLegacyModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409899315277, "dur":102246, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754409899417524, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409899417997, "dur":167548, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.CoreModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409899417893, "dur":167689, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.CoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754409899585587, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409899586030, "dur":50757, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.PerformanceReportingModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409899585929, "dur":50860, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.PerformanceReportingModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409899636790, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409899637085, "dur":169635, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.TextCoreFontEngineModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409899636945, "dur":169847, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754409899806794, "dur":717, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409899808278, "dur":26752, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409899808126, "dur":26946, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754409899835073, "dur":187, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409899835769, "dur":27679, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409899835634, "dur":27816, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409899863451, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409899863709, "dur":92631, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409899863588, "dur":92796, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Collections-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754409899956385, "dur":179, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409899956870, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409899957150, "dur":107935, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Numerics.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409899957015, "dur":108109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Numerics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754409900065125, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900065705, "dur":188078, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409900065573, "dur":188212, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409900253787, "dur":283, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900254212, "dur":263555, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409900254083, "dur":263712, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Tayx.Graphy-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754409900517796, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900518285, "dur":52459, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.EnterpriseServices.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409900518178, "dur":52567, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.EnterpriseServices.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409900570746, "dur":283, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900571278, "dur":91409, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.TerrainModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409900571038, "dur":91650, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.TerrainModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409900662689, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900662836, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900662987, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900663095, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900663244, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900663348, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900663467, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900663603, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900663718, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900663860, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900664002, "dur":288, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900664564, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900664937, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900665316, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900665690, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900665850, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900666221, "dur":193, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900666576, "dur":233, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Collections.LowLevel.ILSupport.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409900666422, "dur":432, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Collections.LowLevel.ILSupport-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754409900666855, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900667309, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900667642, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900668012, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900668513, "dur":224364, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409900668394, "dur":224521, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualEffectGraph.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754409900892916, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900893363, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900893496, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900893647, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409900893783, "dur":114649, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Xml.dll" }}
,{ "pid":12345, "tid":10, "ts":1754409900893781, "dur":114691, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Xml-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754409901008477, "dur":164, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754409901008994, "dur":4394054, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895945379, "dur":10753, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895956143, "dur":869, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895957012, "dur":745, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Core.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895957757, "dur":735, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Console.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895958493, "dur":642, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Configuration.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895959136, "dur":787, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895959923, "dur":698, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895960621, "dur":752, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895961374, "dur":714, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895962089, "dur":1061, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.DataAnnotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895963151, "dur":799, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Annotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895963950, "dur":684, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895964635, "dur":654, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895965289, "dur":720, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Immutable.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895966010, "dur":811, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895966821, "dur":738, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895967559, "dur":783, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Buffers.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895968342, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.AppContext.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895969084, "dur":987, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\SharpYaml.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895970071, "dur":681, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\NiceIO.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895970752, "dur":1470, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895956143, "dur":16080, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895972223, "dur":926, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Observable.Aliases.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895973149, "dur":911, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Linq.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895974890, "dur":548, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Debugger.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895976864, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895977437, "dur":550, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895977988, "dur":606, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.Http.WebRequest.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895978594, "dur":517, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.Http.Formatting.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895979111, "dur":529, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Messaging.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895980830, "dur":599, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.IdentityModel.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895972223, "dur":10027, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895982251, "dur":3747, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895985998, "dur":2016, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895988031, "dur":555, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895988595, "dur":456, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895989062, "dur":769, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895989840, "dur":657, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895990502, "dur":774, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895991294, "dur":778, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895992081, "dur":619, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895992708, "dur":687, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895993404, "dur":641, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895994049, "dur":573, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895994639, "dur":654, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895995306, "dur":831, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895996152, "dur":874, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895997031, "dur":579, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895997615, "dur":553, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895998171, "dur":466, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/machine.config" }}
,{ "pid":12345, "tid":11, "ts":1754409895998638, "dur":498, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409895999140, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409895999289, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409896008414, "dur":80168, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets" }}
,{ "pid":12345, "tid":11, "ts":1754409896090062, "dur":34406, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets" }}
,{ "pid":12345, "tid":11, "ts":1754409896124601, "dur":3066909, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409899192201, "dur":78329, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.TLSModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409899191511, "dur":79054, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TLSModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754409899270566, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409899271051, "dur":111353, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityAnalyticsModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409899270912, "dur":111498, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityAnalyticsModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409899382412, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409899382744, "dur":105433, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.MarshallingModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409899382579, "dur":105641, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.MarshallingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754409899488221, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409899488761, "dur":148589, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.SubsystemsModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409899488636, "dur":148755, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754409899637392, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409899637895, "dur":188059, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.TextCoreTextEngineModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409899637778, "dur":188219, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754409899825998, "dur":166, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409899826529, "dur":26872, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.GraphicsStateCollectionSerializerModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409899826417, "dur":26986, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409899853404, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409899853573, "dur":58317, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409899853570, "dur":58361, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.ShaderLibrary-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754409899911933, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409899912331, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409899912453, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409899912566, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409899912694, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409899912805, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409899912963, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409899913344, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409899913711, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409899914156, "dur":100837, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409899914046, "dur":100988, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.Core-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754409900015036, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900015569, "dur":164839, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409900015444, "dur":165006, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Xml.Linq-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754409900180451, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900180967, "dur":168082, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Configuration.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409900180846, "dur":168204, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Configuration.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409900349051, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900349220, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900349717, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900350420, "dur":80238, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.AnimationModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409900350273, "dur":80425, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AnimationModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754409900430700, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900431337, "dur":32536, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409900431212, "dur":32663, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409900463880, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900464157, "dur":968, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409900464033, "dur":1092, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Recorder.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409900465126, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900465259, "dur":133956, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Drawing.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409900465257, "dur":133959, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Drawing.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409900599218, "dur":438, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900599832, "dur":984, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409900599669, "dur":1148, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409900600818, "dur":1331, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900602920, "dur":58189, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ImageConversionModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409900602157, "dur":59035, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ImageConversionModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754409900661198, "dur":258, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900661777, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900662332, "dur":165358, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityWebRequestWWWModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409900662205, "dur":165528, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestWWWModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754409900827735, "dur":172, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900828462, "dur":109340, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.JSONSerializeModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1754409900828324, "dur":109507, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754409900937832, "dur":261, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900938508, "dur":1271, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900940574, "dur":295, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900942492, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754409900943145, "dur":4459876, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895945402, "dur":10737, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895956140, "dur":824, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\netstandard.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895956964, "dur":852, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\msquic.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895957816, "dur":809, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorrc.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895958625, "dur":658, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorlib.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895959283, "dur":787, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordbi.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895960071, "dur":787, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore_amd64_amd64_7.0.1323.51816.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895960859, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895961567, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\monolinker.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895962245, "dur":1182, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Rocks.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895963428, "dur":690, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Pdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895964118, "dur":674, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Mdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895964792, "dur":614, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895965406, "dur":744, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Registry.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895966151, "dur":865, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895967016, "dur":756, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895967772, "dur":790, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.Core.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895968562, "dur":772, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.DiaSymReader.Native.amd64.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895969334, "dur":1165, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.CSharp.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895970499, "dur":864, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Bcl.HashCode.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895971363, "dur":889, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.exe" }}
,{ "pid":12345, "tid":12, "ts":1754409895956140, "dur":16113, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895972253, "dur":557, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.EnterpriseServices.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895973243, "dur":789, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Drawing.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895974872, "dur":598, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.DirectoryServices.Protocols.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895975470, "dur":582, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Design.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895976052, "dur":523, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Deployment.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895976575, "dur":533, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895977510, "dur":507, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.Services.Client.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895979343, "dur":671, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895972253, "dur":9717, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895982464, "dur":510, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.IO.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895983460, "dur":543, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895984003, "dur":705, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895984708, "dur":512, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895985591, "dur":533, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409895981971, "dur":6888, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895988861, "dur":759, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895989626, "dur":710, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895990344, "dur":713, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895991062, "dur":879, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895991947, "dur":659, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895992612, "dur":709, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895993327, "dur":653, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895993986, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895994581, "dur":825, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895995421, "dur":812, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895996237, "dur":828, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895997071, "dur":618, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895997695, "dur":596, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895998296, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":12, "ts":1754409895998446, "dur":272, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409895998722, "dur":289, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":12, "ts":1754409895999011, "dur":456, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409896024593, "dur":62839, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets" }}
,{ "pid":12345, "tid":12, "ts":1754409896087747, "dur":19048, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1" }}
,{ "pid":12345, "tid":12, "ts":1754409896108507, "dur":3083050, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409899192306, "dur":59246, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409899191576, "dur":60018, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Data.DataSetExtensions-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754409899251595, "dur":216, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409899252369, "dur":48443, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityAnalyticsCommonModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409899252252, "dur":48593, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754409899300846, "dur":163, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409899301257, "dur":39817, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409899301254, "dur":39853, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.IO.Compression.FileSystem-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754409899341108, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409899341609, "dur":82861, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.SpriteShapeModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409899341497, "dur":83016, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754409899424514, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409899425081, "dur":117941, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.InputForUIModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409899424962, "dur":118099, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754409899543063, "dur":218, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409899543786, "dur":57022, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UnityWebRequestAudioModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409899543597, "dur":57212, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityWebRequestAudioModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409899600811, "dur":187, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409899601014, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409899601278, "dur":124503, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\System.ServiceModel.Internals.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409899601139, "dur":124677, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.ServiceModel.Internals-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754409899725816, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409899726809, "dur":340038, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.VideoModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409899726677, "dur":340172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VideoModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409900066850, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409900067141, "dur":55588, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.ARModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409900067004, "dur":55726, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ARModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409900122732, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409900123013, "dur":38533, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.GIModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409900122889, "dur":38658, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.GIModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409900161549, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409900162196, "dur":72899, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.UIModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409900161695, "dur":73401, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UIModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409900235098, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409900235234, "dur":2218, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/netstandard-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754409900237454, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409900238695, "dur":110221, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409900237920, "dur":111025, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Burst-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754409900348946, "dur":162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409900349473, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409900349941, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409900350508, "dur":117586, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\UnityEngine.Physics2DModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409900350392, "dur":117743, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754409900468136, "dur":149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409900468571, "dur":477024, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\ManagedStripped\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409900468569, "dur":477028, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":12, "ts":1754409900945598, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754409900945730, "dur":4457313, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754409905413133, "dur":4385, "ph":"X", "name": "ProfilerWriteOutput" }
,