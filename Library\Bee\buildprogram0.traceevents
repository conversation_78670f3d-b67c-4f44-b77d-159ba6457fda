{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1754406586884701, "dur": 405302, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754406586885958, "dur": 58952, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754406586955856, "dur": 297195, "ph": "X", "name": "RunBuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754406586956798, "dur": 248594, "ph": "X", "name": "PlayerBuildProgramBase.SetupPlayerBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754406586957526, "dur": 48275, "ph": "X", "name": "SetupDataFiles", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754406587006164, "dur": 942, "ph": "X", "name": "SetupCopyPlugins", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754406587033714, "dur": 147594, "ph": "X", "name": "SetupUnityLinker", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754406587260957, "dur": 1020, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754406587261981, "dur": 28014, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754406587263624, "dur": 20899, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754406587298192, "dur": 1329, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754406587297653, "dur": 2125, "ph": "X", "name": "Write chrome-trace events", "args": {} },
