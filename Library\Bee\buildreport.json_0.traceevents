{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754406535652181, "dur": 7128, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754406535659313, "dur": 147420, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754406535806745, "dur": 13030, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 93864, "tid": 64023241, "ts": 1754406537484668, "dur": 1167, "ph": "X", "name": "", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535649787, "dur": 2569, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535652358, "dur": 1816971, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535653723, "dur": 3035, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535656765, "dur": 680, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535657449, "dur": 6096, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535663552, "dur": 218, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535663774, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535663826, "dur": 1114, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535664943, "dur": 3301, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535668249, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535668274, "dur": 109, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535668387, "dur": 964, "ph": "X", "name": "ProcessMessages 471", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535669354, "dur": 7218, "ph": "X", "name": "ReadAsync 471", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535676577, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535676580, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535676634, "dur": 655, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406535677292, "dur": 1766994, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406537444293, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406537444297, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406537444340, "dur": 2031, "ph": "X", "name": "ProcessMessages 15370", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406537446374, "dur": 4860, "ph": "X", "name": "ReadAsync 15370", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406537451255, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406537451268, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406537451380, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406537451382, "dur": 247, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406537451634, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406537451674, "dur": 347, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754406537452024, "dur": 16155, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 64023241, "ts": 1754406537485838, "dur": 29, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 93864, "tid": 8589934592, "ts": 1754406535646590, "dur": 173291, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 93864, "tid": 8589934592, "ts": 1754406535819884, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 8589934592, "ts": 1754406535819890, "dur": 1889, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 93864, "tid": 64023241, "ts": 1754406537485868, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 4294967296, "ts": 1754406535553578, "dur": 1916974, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754406535558134, "dur": 39527, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754406537470612, "dur": 6016, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754406537473679, "dur": 35, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754406537476832, "dur": 22, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 93864, "tid": 64023241, "ts": 1754406537485874, "dur": 101, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754406535651122, "dur":16342, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754406535667477, "dur":188, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754406535667703, "dur":648, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754406535668391, "dur":75, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754406535668466, "dur":1783562, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754406537452028, "dur":214, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754406537452422, "dur":3877, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754406535669223, "dur":925, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1754406535668772, "dur":7440, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1754406535678184, "dur":1768144, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1754406535669238, "dur":1782766, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754406535669552, "dur":1782486, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754406535668991, "dur":153315, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754406535832109, "dur":536, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":4, "ts":1754406535822308, "dur":10342, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754406535832651, "dur":1619438, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754406535669255, "dur":1782750, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754406535669298, "dur":1782705, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754406535669328, "dur":1782695, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754406535669363, "dur":1782638, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754406535669395, "dur":1782637, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754406535669452, "dur":1782559, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754406535669484, "dur":1782538, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754406535669185, "dur":163472, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754406535832657, "dur":1619349, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754406537465509, "dur":407, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 64023241, "ts": 1754406537486635, "dur": 48905, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 64023241, "ts": 1754406537535682, "dur": 2384, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 64023241, "ts": 1754406537483675, "dur": 55368, "ph": "X", "name": "Write chrome-trace events", "args": {} },
