ScriptsOnlyBuild:
  usedScripts:
    Assembly-CSharp:
    - Armor
    - AudioSystem.AudioEventChannel
    - AudioSystem.AudioEventDefinition
    - Bag
    - ConsumableDefinition
    - Helmet
    - InvItemPickup
    - ItemRegistry
    - ItemUniqueId
    - Manual
    - ToolDefinition
    Assembly-CSharp.dll:
    - Armor
    - AudioSystem.AudioEventChannel
    - AudioSystem.AudioEventDefinition
    - AudioSystem.GlobalAudioManager
    - AudioSystem.PlayerAudioHandler
    - AutoSafetySystem
    - Bag
    - BatteryController
    - ClimbingRopeSystem
    - ConsumableDefinition
    - CrosshairManager
    - CurrencyManager
    - CustomCursor
    - DeathManager
    - DebugFlyController
    - DevSceneInitializer
    - DynamicFogController
    - DynamicLightIntensity
    - EquipmentManager
    - FPSControllerSettingsUI
    - FPSPlayerManager
    - FallDamageSystem
    - Flare
    - FlareGun
    - FlarePool
    - GameFlowManager
    - GrabInteraction
    - GrapplingHookSystem
    - HeadBob
    - HeadBobDebug
    - Helmet
    - InteractableObject
    - InteractionManager
    - InteractiveWorldScreen
    - InvDragAndDropManager
    - InvDroppedStorageEquipment
    - InvItemDropping
    - InvItemModelSwapper
    - InvItemPickup
    - InvItemSplitter
    - InvUI
    - ItemUniqueId
    - KinematicCharacterController.FPS.FPSCharacterCamera
    - KinematicCharacterController.FPS.FPSCharacterController
    - KinematicCharacterController.FPS.SpaceshipCameraController
    - KinematicCharacterController.KinematicCharacterMotor
    - KinematicClimbingSystem
    - KinematicPlatform
    - KinematicVaultSystem
    - KinematicWallRun
    - Manual
    - MenuCameraController
    - MirrorOrbitCamera
    - MoneyCounterAnimation
    - MoneyCounterInitializer
    - NotificationManager
    - PauseMenuManager
    - PersistenceManager
    - PlatformButtonController
    - PlayerDebugDisplay
    - PlayerStatus
    - RagdollTumbleSystem
    - SettingsAudioManager
    - ShopSystem
    - ShopUI
    - SpaceshipVehicle
    - StartMenuManager
    - StashSystem
    - StashUI
    - ToolDefinition
    - ToolModelManager
    - ToolSelectionController
    - ToolSelectionManager
    - ToolSelectionUILoader
    - TraderInventory
    - UIPanelManager
    - VehicleInteractable
    - ViewModelHeadBob
    - VoidRescueSystem
    - WakeUpPoint
    - WireCreatorWindow/WireComponent
    - WorldItemManager
    BakeryEditorAssembly.dll:
    - ftRenderLightmap
    BakeryRuntimeAssembly.dll:
    - BakeryLightMesh
    - BakeryLightmapGroup
    - BakeryPointLight
    - BakeryProjectSettings
    - BakerySector
    - BakeryVolume
    - ftGlobalStorage
    - ftLightmapsStorage
    - ftLocalStorage
    Tayx.Graphy.dll:
    - Graphy.Runtime.UI.G_SafeArea
    - Tayx.Graphy.Advanced.G_AdvancedData
    - Tayx.Graphy.Audio.G_AudioGraph
    - Tayx.Graphy.Audio.G_AudioManager
    - Tayx.Graphy.Audio.G_AudioMonitor
    - Tayx.Graphy.Audio.G_AudioText
    - Tayx.Graphy.Fps.G_FpsGraph
    - Tayx.Graphy.Fps.G_FpsManager
    - Tayx.Graphy.Fps.G_FpsMonitor
    - Tayx.Graphy.Fps.G_FpsText
    - Tayx.Graphy.GraphyDebugger
    - Tayx.Graphy.GraphyManager
    - Tayx.Graphy.Ram.G_RamGraph
    - Tayx.Graphy.Ram.G_RamManager
    - Tayx.Graphy.Ram.G_RamMonitor
    - Tayx.Graphy.Ram.G_RamText
    Unity.Formats.Fbx.Editor.dll:
    - UnityEditor.Formats.Fbx.Exporter.ExportSettings
    Unity.PlasticSCM.Editor.dll:
    - Unity.PlasticSCM.Editor.PlasticWindow
    Unity.Recorder.Editor.dll:
    - UnityEditor.Recorder.MovieRecorderEditor
    - UnityEditor.Recorder.MovieRecorderSettings
    - UnityEditor.Recorder.RecorderControllerSettings
    - UnityEditor.Recorder.RecorderPreferencesSettings
    - UnityEditor.Recorder.RecorderSettingsPrefsEditor
    - UnityEditor.Recorder.RecorderWindow
    Unity.RenderPipelines.Core.Editor.dll:
    - UnityEditor.Rendering.DebugWindow
    Unity.RenderPipelines.Core.Runtime:
    - UnityEngine.Rendering.UI.DebugUIHandlerBitField
    - UnityEngine.Rendering.UI.DebugUIHandlerButton
    - UnityEngine.Rendering.UI.DebugUIHandlerCanvas
    - UnityEngine.Rendering.UI.DebugUIHandlerColor
    - UnityEngine.Rendering.UI.DebugUIHandlerContainer
    - UnityEngine.Rendering.UI.DebugUIHandlerEnumField
    - UnityEngine.Rendering.UI.DebugUIHandlerEnumHistory
    - UnityEngine.Rendering.UI.DebugUIHandlerFloatField
    - UnityEngine.Rendering.UI.DebugUIHandlerFoldout
    - UnityEngine.Rendering.UI.DebugUIHandlerGroup
    - UnityEngine.Rendering.UI.DebugUIHandlerHBox
    - UnityEngine.Rendering.UI.DebugUIHandlerIndirectFloatField
    - UnityEngine.Rendering.UI.DebugUIHandlerIndirectToggle
    - UnityEngine.Rendering.UI.DebugUIHandlerIntField
    - UnityEngine.Rendering.UI.DebugUIHandlerMessageBox
    - UnityEngine.Rendering.UI.DebugUIHandlerObject
    - UnityEngine.Rendering.UI.DebugUIHandlerObjectList
    - UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField
    - UnityEngine.Rendering.UI.DebugUIHandlerPanel
    - UnityEngine.Rendering.UI.DebugUIHandlerPersistentCanvas
    - UnityEngine.Rendering.UI.DebugUIHandlerProgressBar
    - UnityEngine.Rendering.UI.DebugUIHandlerRenderingLayerField
    - UnityEngine.Rendering.UI.DebugUIHandlerRow
    - UnityEngine.Rendering.UI.DebugUIHandlerToggle
    - UnityEngine.Rendering.UI.DebugUIHandlerToggleHistory
    - UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    - UnityEngine.Rendering.UI.DebugUIHandlerVBox
    - UnityEngine.Rendering.UI.DebugUIHandlerValue
    - UnityEngine.Rendering.UI.DebugUIHandlerValueTuple
    - UnityEngine.Rendering.UI.DebugUIHandlerVector2
    - UnityEngine.Rendering.UI.DebugUIHandlerVector3
    - UnityEngine.Rendering.UI.DebugUIHandlerVector4
    - UnityEngine.Rendering.UI.UIFoldout
    Unity.RenderPipelines.Core.Runtime.dll:
    - UnityEngine.Rendering.ProbeVolume
    - UnityEngine.Rendering.ProbeVolumeBakingSet
    - UnityEngine.Rendering.ProbeVolumePerSceneData
    - UnityEngine.Rendering.ProbeVolumesOptions
    - UnityEngine.Rendering.Volume
    - UnityEngine.Rendering.VolumeProfile
    Unity.RenderPipelines.HighDefinition.Editor.dll:
    - UnityEditor.Rendering.HighDefinition.HDProjectSettings
    - UnityEditor.Rendering.HighDefinition.HDUserSettings
    Unity.RenderPipelines.HighDefinition.Runtime.dll:
    - UnityEngine.Rendering.HighDefinition.Bloom
    - UnityEngine.Rendering.HighDefinition.ChannelMixer
    - UnityEngine.Rendering.HighDefinition.ChromaticAberration
    - UnityEngine.Rendering.HighDefinition.CloudLayer
    - UnityEngine.Rendering.HighDefinition.ColorAdjustments
    - UnityEngine.Rendering.HighDefinition.ColorCurves
    - UnityEngine.Rendering.HighDefinition.ComputeMaterialLibrary
    - UnityEngine.Rendering.HighDefinition.ContactShadows
    - UnityEngine.Rendering.HighDefinition.CustomPassVolume
    - UnityEngine.Rendering.HighDefinition.DepthOfField
    - UnityEngine.Rendering.HighDefinition.DiffusionProfileList
    - UnityEngine.Rendering.HighDefinition.DiffusionProfileSettings
    - UnityEngine.Rendering.HighDefinition.Exposure
    - UnityEngine.Rendering.HighDefinition.FilmGrain
    - UnityEngine.Rendering.HighDefinition.Fog
    - UnityEngine.Rendering.HighDefinition.GlobalIllumination
    - UnityEngine.Rendering.HighDefinition.GradientSky
    - UnityEngine.Rendering.HighDefinition.HDAdditionalCameraData
    - UnityEngine.Rendering.HighDefinition.HDAdditionalLightData
    - UnityEngine.Rendering.HighDefinition.HDRISky
    - UnityEngine.Rendering.HighDefinition.HDRenderPipelineAsset
    - UnityEngine.Rendering.HighDefinition.HDRenderPipelineGlobalSettings
    - UnityEngine.Rendering.HighDefinition.HDShadowSettings
    - UnityEngine.Rendering.HighDefinition.HighQualityLineRenderingVolumeComponent
    - UnityEngine.Rendering.HighDefinition.IndirectLightingController
    - UnityEngine.Rendering.HighDefinition.LensDistortion
    - UnityEngine.Rendering.HighDefinition.LiftGammaGain
    - UnityEngine.Rendering.HighDefinition.LightCluster
    - UnityEngine.Rendering.HighDefinition.LocalVolumetricFog
    - UnityEngine.Rendering.HighDefinition.MicroShadowing
    - UnityEngine.Rendering.HighDefinition.MotionBlur
    - UnityEngine.Rendering.HighDefinition.PaniniProjection
    - UnityEngine.Rendering.HighDefinition.PathTracing
    - UnityEngine.Rendering.HighDefinition.PhysicallyBasedSky
    - UnityEngine.Rendering.HighDefinition.RayTracingSettings
    - UnityEngine.Rendering.HighDefinition.RecursiveRendering
    - UnityEngine.Rendering.HighDefinition.SceneObjectIDMapSceneAsset
    - UnityEngine.Rendering.HighDefinition.ScreenSpaceAmbientOcclusion
    - UnityEngine.Rendering.HighDefinition.ScreenSpaceLensFlare
    - UnityEngine.Rendering.HighDefinition.ScreenSpaceReflection
    - UnityEngine.Rendering.HighDefinition.ScreenSpaceRefraction
    - UnityEngine.Rendering.HighDefinition.ShadowsMidtonesHighlights
    - UnityEngine.Rendering.HighDefinition.SplitToning
    - UnityEngine.Rendering.HighDefinition.StaticLightingSky
    - UnityEngine.Rendering.HighDefinition.SubSurfaceScattering
    - UnityEngine.Rendering.HighDefinition.Tonemapping
    - UnityEngine.Rendering.HighDefinition.Vignette
    - UnityEngine.Rendering.HighDefinition.VisualEnvironment
    - UnityEngine.Rendering.HighDefinition.VolumetricClouds
    - UnityEngine.Rendering.HighDefinition.WaterRendering
    - UnityEngine.Rendering.HighDefinition.WaterSurface
    - UnityEngine.Rendering.HighDefinition.WhiteBalance
    Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime:
    - UnityEngine.Rendering.HighDefinition.RequiredSettingsSO_HDRP
    Unity.Rider.Editor.dll:
    - Packages.Rider.Editor.UnitTesting.CallbackData
    Unity.ShaderGraph.Editor.dll:
    - UnityEditor.ShaderGraph.Drawing.MaterialGraphEditWindow
    Unity.TextMeshPro:
    - TMPro.TMP_FontAsset
    - TMPro.TMP_Settings
    - TMPro.TMP_SpriteAsset
    - TMPro.TMP_StyleSheet
    Unity.TextMeshPro.dll:
    - TMPro.TMP_FontAsset
    Unity.VisualEffectGraph.Runtime.dll:
    - UnityEngine.VFX.VFXRuntimeResources
    UnityEditor.TestRunner.dll:
    - UnityEditor.TestTools.TestRunner.Api.CallbacksHolder
    - UnityEditor.TestTools.TestRunner.CommandLineTest.RunData
    - UnityEditor.TestTools.TestRunner.TestListCacheData
    - UnityEditor.TestTools.TestRunner.TestRun.TestJobDataHolder
    UnityEngine.UI:
    - UnityEngine.EventSystems.EventTrigger
    - UnityEngine.UI.Button
    - UnityEngine.UI.CanvasScaler
    - UnityEngine.UI.ContentSizeFitter
    - UnityEngine.UI.GraphicRaycaster
    - UnityEngine.UI.HorizontalLayoutGroup
    - UnityEngine.UI.Image
    - UnityEngine.UI.LayoutElement
    - UnityEngine.UI.Mask
    - UnityEngine.UI.ScrollRect
    - UnityEngine.UI.Scrollbar
    - UnityEngine.UI.Text
    - UnityEngine.UI.Toggle
    - UnityEngine.UI.VerticalLayoutGroup
    UnityEngine.UI.dll:
    - UnityEngine.EventSystems.EventSystem
    - UnityEngine.EventSystems.StandaloneInputModule
    - UnityEngine.UI.CanvasScaler
    - UnityEngine.UI.Image
    - UnityEngine.UI.Shadow
    - UnityEngine.UI.Text
  serializedClasses:
    Assembly-CSharp:
    - AudioSystem.AudioEventData
    - AudioSystem.SurfaceAudioVariation
    - Dimensions
    - GameSaveData
    - InteractiveWorldScreen/ScreenButton
    - InteractiveWorldScreen/ScreenImage
    - InteractiveWorldScreen/ScreenText
    - InvItemDropping/ItemModelDictionary
    - KinematicPlatform/PlatformWaypoint
    - Manual/ItemInformation
    - MirrorOrbitCamera/TargetSettings
    - NotificationManager/NotificationSettings
    - Outline
    - SpaceshipVehicle/DirectionMapping
    - ToolModelManager/ToolModelData
    - ToolSelectionManager/ToolSlotInfo
    - TraderInventory/StockItem
    BakeryRuntimeAssembly:
    - ftGlobalStorage/AdjustedMesh
    - ftGlobalStorage/AtlasPacker
    - ftLightmapsStorage/LightData
    - ftLightmapsStorage/SectorData
    Tayx.Graphy:
    - Tayx.Graphy.GraphyDebugger/DebugCondition
    - Tayx.Graphy.GraphyDebugger/DebugPacket
    Unity.RenderPipelines.Core.Runtime:
    - UnityEngine.Rendering.APVLeakReductionModeParameter
    - UnityEngine.Rendering.AnimationCurveParameter
    - UnityEngine.Rendering.BitArray128
    - UnityEngine.Rendering.BoolParameter
    - UnityEngine.Rendering.ClampedFloatParameter
    - UnityEngine.Rendering.ClampedIntParameter
    - UnityEngine.Rendering.ColorParameter
    - UnityEngine.Rendering.CubemapParameter
    - UnityEngine.Rendering.EnumParameter`1
    - UnityEngine.Rendering.FloatParameter
    - UnityEngine.Rendering.FloatRangeParameter
    - UnityEngine.Rendering.GlobalDynamicResolutionSettings
    - UnityEngine.Rendering.IncludeAdditionalRPAssets
    - UnityEngine.Rendering.IntParameter
    - UnityEngine.Rendering.LayerMaskParameter
    - UnityEngine.Rendering.LightmapSamplingSettings
    - UnityEngine.Rendering.MaterialParameter
    - UnityEngine.Rendering.MinFloatParameter
    - UnityEngine.Rendering.MinIntParameter
    - UnityEngine.Rendering.NoInterpClampedIntParameter
    - UnityEngine.Rendering.NoInterpIntParameter
    - UnityEngine.Rendering.NoInterpMinFloatParameter
    - UnityEngine.Rendering.NoInterpVector2Parameter
    - UnityEngine.Rendering.ProbeVolumeBakingResources
    - UnityEngine.Rendering.ProbeVolumeBlendingTextureMemoryBudget
    - UnityEngine.Rendering.ProbeVolumeDebugResources
    - UnityEngine.Rendering.ProbeVolumeGlobalSettings
    - UnityEngine.Rendering.ProbeVolumeRuntimeResources
    - UnityEngine.Rendering.ProbeVolumeSHBands
    - UnityEngine.Rendering.ProbeVolumeSceneData
    - UnityEngine.Rendering.ProbeVolumeTextureMemoryBudget
    - UnityEngine.Rendering.RenderGraphGlobalSettings
    - UnityEngine.Rendering.RenderGraphModule.Util.RenderGraphUtilsResources
    - UnityEngine.Rendering.RenderPipelineGraphicsSettingsContainer
    - UnityEngine.Rendering.RenderingLayerMaskParameter
    - UnityEngine.Rendering.STP/RuntimeResources
    - UnityEngine.Rendering.SerializedDictionary`2
    - UnityEngine.Rendering.ShaderStrippingSetting
    - UnityEngine.Rendering.Texture2DParameter
    - UnityEngine.Rendering.Texture3DParameter
    - UnityEngine.Rendering.TextureCurve
    - UnityEngine.Rendering.TextureCurveParameter
    - UnityEngine.Rendering.TextureParameter
    - UnityEngine.Rendering.UI.DebugUIPrefabBundle
    - UnityEngine.Rendering.Vector2Parameter
    - UnityEngine.Rendering.Vector3Parameter
    - UnityEngine.Rendering.Vector4Parameter
    - UnityEngine.Rendering.VolumeParameter`1
    - UnityEngine.Rendering.VrsRenderPipelineRuntimeResources
    Unity.RenderPipelines.GPUDriven.Runtime:
    - UnityEngine.Rendering.GPUResidentDrawerResources
    Unity.RenderPipelines.HighDefinition.Editor:
    - UnityEditor.Rendering.HighDefinition.HDRenderingLayersLimitSettings
    Unity.RenderPipelines.HighDefinition.Runtime:
    - UnityEngine.Rendering.GlobalXRSettings
    - UnityEngine.Rendering.HighDefinition.AdaptationModeParameter
    - UnityEngine.Rendering.HighDefinition.AnalyticDerivativeSettings
    - UnityEngine.Rendering.HighDefinition.BackplateTypeParameter
    - UnityEngine.Rendering.HighDefinition.BloomResolutionParameter
    - UnityEngine.Rendering.HighDefinition.BoolScalableSetting
    - UnityEngine.Rendering.HighDefinition.BoolScalableSettingValue
    - UnityEngine.Rendering.HighDefinition.CameraClampModeParameter
    - UnityEngine.Rendering.HighDefinition.CascadeEndBorderParameter
    - UnityEngine.Rendering.HighDefinition.CascadePartitionSplitParameter
    - UnityEngine.Rendering.HighDefinition.CloudDistortionMode
    - UnityEngine.Rendering.HighDefinition.CloudLayer/CloudMap
    - UnityEngine.Rendering.HighDefinition.CloudMapMode
    - UnityEngine.Rendering.HighDefinition.CloudResolution
    - UnityEngine.Rendering.HighDefinition.CloudShadowsResolution
    - UnityEngine.Rendering.HighDefinition.ColorGradingSettings
    - UnityEngine.Rendering.HighDefinition.ComputeMaterialDictionary
    - UnityEngine.Rendering.HighDefinition.CookieAtlasGraphicsFormat
    - UnityEngine.Rendering.HighDefinition.CookieAtlasResolution
    - UnityEngine.Rendering.HighDefinition.CubeReflectionResolution
    - UnityEngine.Rendering.HighDefinition.CustomPostProcessOrdersSettings
    - UnityEngine.Rendering.HighDefinition.CustomPostProcessVolumeComponentList
    - UnityEngine.Rendering.HighDefinition.DepthOfFieldModeParameter
    - UnityEngine.Rendering.HighDefinition.DepthOfFieldResolutionParameter
    - UnityEngine.Rendering.HighDefinition.DiffusionProfile
    - UnityEngine.Rendering.HighDefinition.DiffusionProfileDefaultSettings
    - UnityEngine.Rendering.HighDefinition.DiffusionProfileSettingsParameter
    - UnityEngine.Rendering.HighDefinition.DrawRenderersCustomPass
    - UnityEngine.Rendering.HighDefinition.EnvUpdateParameter
    - UnityEngine.Rendering.HighDefinition.ExposureModeParameter
    - UnityEngine.Rendering.HighDefinition.FallbackHDRTonemapParameter
    - UnityEngine.Rendering.HighDefinition.FilmGrainLookupParameter
    - UnityEngine.Rendering.HighDefinition.FloatScalableSetting
    - UnityEngine.Rendering.HighDefinition.FocusDistanceModeParameter
    - UnityEngine.Rendering.HighDefinition.FogColorParameter
    - UnityEngine.Rendering.HighDefinition.FogControlParameter
    - UnityEngine.Rendering.HighDefinition.FogDenoisingModeParameter
    - UnityEngine.Rendering.HighDefinition.FogTypeParameter
    - UnityEngine.Rendering.HighDefinition.FrameSettings
    - UnityEngine.Rendering.HighDefinition.FrameSettingsOverrideMask
    - UnityEngine.Rendering.HighDefinition.FullScreenCustomPass
    - UnityEngine.Rendering.HighDefinition.GPUCacheSettingSRP
    - UnityEngine.Rendering.HighDefinition.GlobalDecalSettings
    - UnityEngine.Rendering.HighDefinition.GlobalGPUResidentDrawerSettings
    - UnityEngine.Rendering.HighDefinition.GlobalLightLoopSettings
    - UnityEngine.Rendering.HighDefinition.GlobalLightingQualitySettings
    - UnityEngine.Rendering.HighDefinition.GlobalLowResolutionTransparencySettings
    - UnityEngine.Rendering.HighDefinition.GlobalPostProcessSettings
    - UnityEngine.Rendering.HighDefinition.GlobalPostProcessingQualitySettings
    - UnityEngine.Rendering.HighDefinition.HDPhysicalCamera
    - UnityEngine.Rendering.HighDefinition.HDRACESPresetParameter
    - UnityEngine.Rendering.HighDefinition.HDRISky/DistortionMode
    - UnityEngine.Rendering.HighDefinition.HDRPDefaultVolumeProfileSettings
    - UnityEngine.Rendering.HighDefinition.HDRPRayTracingResources
    - UnityEngine.Rendering.HighDefinition.HDRenderPipelineEditorAssets
    - UnityEngine.Rendering.HighDefinition.HDRenderPipelineEditorMaterials
    - UnityEngine.Rendering.HighDefinition.HDRenderPipelineEditorShaders
    - UnityEngine.Rendering.HighDefinition.HDRenderPipelineEditorTextures
    - UnityEngine.Rendering.HighDefinition.HDRenderPipelineRuntimeAssets
    - UnityEngine.Rendering.HighDefinition.HDRenderPipelineRuntimeMaterials
    - UnityEngine.Rendering.HighDefinition.HDRenderPipelineRuntimeShaders
    - UnityEngine.Rendering.HighDefinition.HDRenderPipelineRuntimeTextures
    - UnityEngine.Rendering.HighDefinition.HDShadowInitParameters
    - UnityEngine.Rendering.HighDefinition.HDShadowInitParameters/HDShadowAtlasInitParams
    - UnityEngine.Rendering.HighDefinition.HighQualityLineRenderingVolumeComponent/LinesCompositionModeParameter
    - UnityEngine.Rendering.HighDefinition.HighQualityLineRenderingVolumeComponent/LinesSortingQualityParameter
    - UnityEngine.Rendering.HighDefinition.IndirectLightingController/LightLayerEnumParameter
    - UnityEngine.Rendering.HighDefinition.IntScalableSetting
    - UnityEngine.Rendering.HighDefinition.IntScalableSettingValue
    - UnityEngine.Rendering.HighDefinition.LensSettings
    - UnityEngine.Rendering.HighDefinition.LocalVolumetricFogArtistParameters
    - UnityEngine.Rendering.HighDefinition.LocalVolumetricFogResolution
    - UnityEngine.Rendering.HighDefinition.LookDevVolumeProfileSettings
    - UnityEngine.Rendering.HighDefinition.LuminanceSourceParameter
    - UnityEngine.Rendering.HighDefinition.MeteringModeParameter
    - UnityEngine.Rendering.HighDefinition.NeutralRangeReductionModeParameter
    - UnityEngine.Rendering.HighDefinition.ObsoleteFrameSettings
    - UnityEngine.Rendering.HighDefinition.ObsoleteLightLoopSettings
    - UnityEngine.Rendering.HighDefinition.PhysicallyBasedSky/RenderingMode
    - UnityEngine.Rendering.HighDefinition.PhysicallyBasedSkyModel
    - UnityEngine.Rendering.HighDefinition.PlanarReflectionAtlasResolution
    - UnityEngine.Rendering.HighDefinition.RTASBuildModeParameter
    - UnityEngine.Rendering.HighDefinition.RTASCullingModeParameter
    - UnityEngine.Rendering.HighDefinition.RayCastingModeParameter
    - UnityEngine.Rendering.HighDefinition.RayMarchingFallbackHierarchyParameter
    - UnityEngine.Rendering.HighDefinition.RayTracingFallbackHierachyParameter
    - UnityEngine.Rendering.HighDefinition.RayTracingModeParameter
    - UnityEngine.Rendering.HighDefinition.ReflectionAndPlanarProbeFormat
    - UnityEngine.Rendering.HighDefinition.ReflectionProbeTextureCacheResolution
    - UnityEngine.Rendering.HighDefinition.RenderGraphSettings
    - UnityEngine.Rendering.HighDefinition.RenderPipelineSettings
    - UnityEngine.Rendering.HighDefinition.RenderPipelineSettings/LightSettings
    - UnityEngine.Rendering.HighDefinition.RenderPipelineSettings/PlanarReflectionAtlasResolutionScalableSetting
    - UnityEngine.Rendering.HighDefinition.RenderPipelineSettings/ReflectionProbeResolutionScalableSetting
    - UnityEngine.Rendering.HighDefinition.RenderingPathFrameSettings
    - UnityEngine.Rendering.HighDefinition.RenderingSpace
    - UnityEngine.Rendering.HighDefinition.SSRAlgoParameter
    - UnityEngine.Rendering.HighDefinition.ScalableSettingLevelParameter
    - UnityEngine.Rendering.HighDefinition.ScalableSettingSchemaId
    - UnityEngine.Rendering.HighDefinition.ScreenSpaceLensFlareResolutionParameter
    - UnityEngine.Rendering.HighDefinition.SeedModeParameter
    - UnityEngine.Rendering.HighDefinition.SkyAmbientModeParameter
    - UnityEngine.Rendering.HighDefinition.SkyImportanceSamplingParameter
    - UnityEngine.Rendering.HighDefinition.SkyIntensityParameter
    - UnityEngine.Rendering.HighDefinition.SkyResolution
    - UnityEngine.Rendering.HighDefinition.SpecularFadeSettings
    - UnityEngine.Rendering.HighDefinition.TargetMidGrayParameter
    - UnityEngine.Rendering.HighDefinition.TonemappingModeParameter
    - UnityEngine.Rendering.HighDefinition.VignetteModeParameter
    - UnityEngine.Rendering.HighDefinition.VirtualTexturingSettingsSRP
    - UnityEngine.Rendering.HighDefinition.VisualEnvironment/PlanetMode
    - UnityEngine.Rendering.HighDefinition.VolumetricClouds/CloudControl
    - UnityEngine.Rendering.HighDefinition.VolumetricClouds/CloudErosionNoise
    - UnityEngine.Rendering.HighDefinition.VolumetricClouds/CloudFadeInMode
    - UnityEngine.Rendering.HighDefinition.VolumetricClouds/CloudMapResolution
    - UnityEngine.Rendering.HighDefinition.VolumetricClouds/CloudPresets
    - UnityEngine.Rendering.HighDefinition.VolumetricClouds/CloudShadowResolution
    - UnityEngine.Rendering.HighDefinition.VolumetricClouds/CloudSimpleMode
    - UnityEngine.Rendering.HighDefinition.VolumetricCloudsRuntimeResources
    - UnityEngine.Rendering.HighDefinition.WaterSystemGlobalSettings
    - UnityEngine.Rendering.HighDefinition.WaterSystemRuntimeResources
    - UnityEngine.Rendering.HighDefinition.WindOrientationParameter
    - UnityEngine.Rendering.HighDefinition.WindParameter/WindParamaterValue
    - UnityEngine.Rendering.HighDefinition.WindSpeedParameter
    - UnityEngine.Rendering.LineRendering/MemoryBudget
    Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime:
    - UnityEngine.Rendering.HighDefinition.RequiredSettingHDRP
    Unity.TextMeshPro:
    - TMPro.FaceInfo_Legacy
    - TMPro.FontAssetCreationSettings
    - TMPro.KerningTable
    - TMPro.TMP_Character
    - TMPro.TMP_FontFeatureTable
    - TMPro.TMP_FontWeightPair
    - TMPro.TMP_Sprite
    - TMPro.TMP_SpriteCharacter
    - TMPro.TMP_SpriteGlyph
    - TMPro.TMP_Style
    UnityEngine.CoreModule:
    - UnityEngine.Bounds
    - UnityEngine.Events.ArgumentCache
    - UnityEngine.Events.PersistentCallGroup
    - UnityEngine.Events.PersistentListenerMode
    - UnityEngine.Events.UnityEvent
    - UnityEngine.RectOffset
    - UnityEngine.Rendering.RenderPipelineGraphicsSettingsCollection
    UnityEngine.TextCoreFontEngineModule:
    - UnityEngine.TextCore.FaceInfo
    - UnityEngine.TextCore.Glyph
    - UnityEngine.TextCore.GlyphMetrics
    - UnityEngine.TextCore.GlyphRect
    - UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord
    - UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord
    - UnityEngine.TextCore.LowLevel.GlyphValueRecord
    UnityEngine.UI:
    - UnityEngine.EventSystems.EventTrigger/Entry
    - UnityEngine.EventSystems.EventTrigger/TriggerEvent
    - UnityEngine.UI.AnimationTriggers
    - UnityEngine.UI.Button/ButtonClickedEvent
    - UnityEngine.UI.ColorBlock
    - UnityEngine.UI.FontData
    - UnityEngine.UI.MaskableGraphic/CullStateChangedEvent
    - UnityEngine.UI.Navigation
    - UnityEngine.UI.ScrollRect/ScrollRectEvent
    - UnityEngine.UI.Scrollbar/ScrollEvent
    - UnityEngine.UI.SpriteState
    - UnityEngine.UI.Toggle/ToggleEvent
    UnityEngine.UIElementsModule:
    - UnityEngine.UIElements.Button/UxmlFactory
    - UnityEngine.UIElements.Button/UxmlSerializedData
    - UnityEngine.UIElements.DropdownField/UxmlFactory
    - UnityEngine.UIElements.DropdownField/UxmlSerializedData
    - UnityEngine.UIElements.DynamicAtlasSettings
    - UnityEngine.UIElements.Image/UxmlFactory
    - UnityEngine.UIElements.Image/UxmlSerializedData
    - UnityEngine.UIElements.Label/UxmlFactory
    - UnityEngine.UIElements.Label/UxmlSerializedData
    - UnityEngine.UIElements.ScrollView/UxmlFactory
    - UnityEngine.UIElements.ScrollView/UxmlSerializedData
    - UnityEngine.UIElements.Slider/UxmlFactory
    - UnityEngine.UIElements.Slider/UxmlSerializedData
    - UnityEngine.UIElements.StyleComplexSelector
    - UnityEngine.UIElements.StyleProperty
    - UnityEngine.UIElements.StyleRule
    - UnityEngine.UIElements.StyleSelector
    - UnityEngine.UIElements.StyleSelectorPart
    - UnityEngine.UIElements.StyleSheet/ImportStruct
    - UnityEngine.UIElements.StyleSheets.Dimension
    - UnityEngine.UIElements.StyleSheets.ScalableImage
    - UnityEngine.UIElements.StyleValueHandle
    - UnityEngine.UIElements.Toggle/UxmlFactory
    - UnityEngine.UIElements.Toggle/UxmlSerializedData
    - UnityEngine.UIElements.UxmlNamespaceDefinition
    - UnityEngine.UIElements.UxmlRootElementFactory
    - UnityEngine.UIElements.VisualElement/UxmlFactory
    - UnityEngine.UIElements.VisualElement/UxmlSerializedData
    - UnityEngine.UIElements.VisualElementAsset
  methodsToPreserve:
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerButton
    methodName: OnAction
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerPanel
    methodName: OnScrollbarClicked
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerPanel
    methodName: OnScrollbarClicked
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerPanel
    methodName: ResetDebugManager
  - assembly: Assembly-CSharp
    fullTypeName: KinematicPlatform
    methodName: StartMoving
  sceneClasses:
    Assets/_Game/Scenes/Main.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 54
      Script: {instanceID: 0}
    - Class: 64
      Script: {instanceID: 0}
    - Class: 65
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 82
      Script: {instanceID: 0}
    - Class: 83
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 1558}
    - Class: 114
      Script: {instanceID: 1560}
    - Class: 114
      Script: {instanceID: 1562}
    - Class: 114
      Script: {instanceID: 1584}
    - Class: 114
      Script: {instanceID: 1586}
    - Class: 114
      Script: {instanceID: 34866}
    - Class: 114
      Script: {instanceID: 34888}
    - Class: 114
      Script: {instanceID: 34946}
    - Class: 114
      Script: {instanceID: 34956}
    - Class: 114
      Script: {instanceID: 34984}
    - Class: 114
      Script: {instanceID: 35054}
    - Class: 114
      Script: {instanceID: 35084}
    - Class: 114
      Script: {instanceID: 35272}
    - Class: 114
      Script: {instanceID: 35342}
    - Class: 114
      Script: {instanceID: 35360}
    - Class: 114
      Script: {instanceID: 35368}
    - Class: 114
      Script: {instanceID: 35384}
    - Class: 114
      Script: {instanceID: 35474}
    - Class: 114
      Script: {instanceID: 35914}
    - Class: 114
      Script: {instanceID: 36004}
    - Class: 114
      Script: {instanceID: 36056}
    - Class: 114
      Script: {instanceID: 36150}
    - Class: 114
      Script: {instanceID: 36168}
    - Class: 114
      Script: {instanceID: 36226}
    - Class: 114
      Script: {instanceID: 36378}
    - Class: 114
      Script: {instanceID: 36594}
    - Class: 114
      Script: {instanceID: 36690}
    - Class: 114
      Script: {instanceID: 36926}
    - Class: 114
      Script: {instanceID: 36940}
    - Class: 114
      Script: {instanceID: 36988}
    - Class: 114
      Script: {instanceID: 37128}
    - Class: 114
      Script: {instanceID: 37148}
    - Class: 114
      Script: {instanceID: 37176}
    - Class: 114
      Script: {instanceID: 37356}
    - Class: 114
      Script: {instanceID: 37544}
    - Class: 114
      Script: {instanceID: 37554}
    - Class: 114
      Script: {instanceID: 37794}
    - Class: 114
      Script: {instanceID: 37928}
    - Class: 114
      Script: {instanceID: 37950}
    - Class: 114
      Script: {instanceID: 38412}
    - Class: 114
      Script: {instanceID: 38432}
    - Class: 114
      Script: {instanceID: 38458}
    - Class: 114
      Script: {instanceID: 38582}
    - Class: 114
      Script: {instanceID: 38628}
    - Class: 114
      Script: {instanceID: 38630}
    - Class: 114
      Script: {instanceID: 38692}
    - Class: 114
      Script: {instanceID: 39110}
    - Class: 114
      Script: {instanceID: 39242}
    - Class: 114
      Script: {instanceID: 39272}
    - Class: 114
      Script: {instanceID: 39308}
    - Class: 114
      Script: {instanceID: 39676}
    - Class: 114
      Script: {instanceID: 39856}
    - Class: 114
      Script: {instanceID: 39938}
    - Class: 114
      Script: {instanceID: 40198}
    - Class: 114
      Script: {instanceID: 40410}
    - Class: 114
      Script: {instanceID: 40796}
    - Class: 114
      Script: {instanceID: 40884}
    - Class: 114
      Script: {instanceID: 41030}
    - Class: 114
      Script: {instanceID: 41048}
    - Class: 114
      Script: {instanceID: 41086}
    - Class: 114
      Script: {instanceID: 41394}
    - Class: 114
      Script: {instanceID: 41474}
    - Class: 114
      Script: {instanceID: 41698}
    - Class: 114
      Script: {instanceID: 42060}
    - Class: 114
      Script: {instanceID: 42108}
    - Class: 114
      Script: {instanceID: 42154}
    - Class: 114
      Script: {instanceID: 42292}
    - Class: 114
      Script: {instanceID: 42438}
    - Class: 114
      Script: {instanceID: 42840}
    - Class: 114
      Script: {instanceID: 42900}
    - Class: 114
      Script: {instanceID: 42912}
    - Class: 114
      Script: {instanceID: 42926}
    - Class: 114
      Script: {instanceID: 42968}
    - Class: 114
      Script: {instanceID: 43010}
    - Class: 114
      Script: {instanceID: 43184}
    - Class: 114
      Script: {instanceID: 43764}
    - Class: 114
      Script: {instanceID: 43766}
    - Class: 114
      Script: {instanceID: 43886}
    - Class: 114
      Script: {instanceID: 43994}
    - Class: 114
      Script: {instanceID: 43998}
    - Class: 114
      Script: {instanceID: 44180}
    - Class: 114
      Script: {instanceID: 44378}
    - Class: 114
      Script: {instanceID: 44448}
    - Class: 114
      Script: {instanceID: 44698}
    - Class: 114
      Script: {instanceID: 44888}
    - Class: 114
      Script: {instanceID: 45044}
    - Class: 114
      Script: {instanceID: 45228}
    - Class: 114
      Script: {instanceID: 45278}
    - Class: 114
      Script: {instanceID: 45524}
    - Class: 114
      Script: {instanceID: 45602}
    - Class: 114
      Script: {instanceID: 45818}
    - Class: 114
      Script: {instanceID: 45882}
    - Class: 114
      Script: {instanceID: 45904}
    - Class: 114
      Script: {instanceID: 45970}
    - Class: 114
      Script: {instanceID: 46156}
    - Class: 114
      Script: {instanceID: 46354}
    - Class: 114
      Script: {instanceID: 46434}
    - Class: 114
      Script: {instanceID: 46616}
    - Class: 114
      Script: {instanceID: 46774}
    - Class: 114
      Script: {instanceID: 46784}
    - Class: 114
      Script: {instanceID: 46806}
    - Class: 114
      Script: {instanceID: 46910}
    - Class: 114
      Script: {instanceID: 46918}
    - Class: 114
      Script: {instanceID: 46950}
    - Class: 114
      Script: {instanceID: 47092}
    - Class: 114
      Script: {instanceID: 47134}
    - Class: 114
      Script: {instanceID: 47148}
    - Class: 114
      Script: {instanceID: 47172}
    - Class: 114
      Script: {instanceID: 47178}
    - Class: 114
      Script: {instanceID: 47200}
    - Class: 114
      Script: {instanceID: 47218}
    - Class: 114
      Script: {instanceID: 47544}
    - Class: 114
      Script: {instanceID: 47764}
    - Class: 114
      Script: {instanceID: 47836}
    - Class: 114
      Script: {instanceID: 47870}
    - Class: 114
      Script: {instanceID: 48144}
    - Class: 114
      Script: {instanceID: 48168}
    - Class: 114
      Script: {instanceID: 48194}
    - Class: 114
      Script: {instanceID: 48298}
    - Class: 114
      Script: {instanceID: 48332}
    - Class: 114
      Script: {instanceID: 48350}
    - Class: 114
      Script: {instanceID: 48576}
    - Class: 114
      Script: {instanceID: 48580}
    - Class: 114
      Script: {instanceID: 48882}
    - Class: 114
      Script: {instanceID: 49006}
    - Class: 114
      Script: {instanceID: 49278}
    - Class: 114
      Script: {instanceID: 49312}
    - Class: 114
      Script: {instanceID: 49356}
    - Class: 114
      Script: {instanceID: 49368}
    - Class: 114
      Script: {instanceID: 49430}
    - Class: 114
      Script: {instanceID: 49526}
    - Class: 114
      Script: {instanceID: 49816}
    - Class: 114
      Script: {instanceID: 49926}
    - Class: 114
      Script: {instanceID: 50304}
    - Class: 114
      Script: {instanceID: 50310}
    - Class: 114
      Script: {instanceID: 50322}
    - Class: 114
      Script: {instanceID: 2879840}
    - Class: 114
      Script: {instanceID: 2881576}
    - Class: 114
      Script: {instanceID: 2886016}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 117
      Script: {instanceID: 0}
    - Class: 120
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 135
      Script: {instanceID: 0}
    - Class: 136
      Script: {instanceID: 0}
    - Class: 154
      Script: {instanceID: 0}
    - Class: 156
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 169
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 198
      Script: {instanceID: 0}
    - Class: 199
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 218
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    - Class: 241
      Script: {instanceID: 0}
    - Class: 243
      Script: {instanceID: 0}
    - Class: 245
      Script: {instanceID: 0}
    - Class: 258
      Script: {instanceID: 0}
    - Class: 850595691
      Script: {instanceID: 0}
    Assets/_Game/Scenes/StartMenu.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 49
      Script: {instanceID: 0}
    - Class: 54
      Script: {instanceID: 0}
    - Class: 64
      Script: {instanceID: 0}
    - Class: 65
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 82
      Script: {instanceID: 0}
    - Class: 83
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 1558}
    - Class: 114
      Script: {instanceID: 1560}
    - Class: 114
      Script: {instanceID: 1562}
    - Class: 114
      Script: {instanceID: 1584}
    - Class: 114
      Script: {instanceID: 1586}
    - Class: 114
      Script: {instanceID: 34866}
    - Class: 114
      Script: {instanceID: 35054}
    - Class: 114
      Script: {instanceID: 35200}
    - Class: 114
      Script: {instanceID: 35360}
    - Class: 114
      Script: {instanceID: 35474}
    - Class: 114
      Script: {instanceID: 35498}
    - Class: 114
      Script: {instanceID: 35798}
    - Class: 114
      Script: {instanceID: 35968}
    - Class: 114
      Script: {instanceID: 36004}
    - Class: 114
      Script: {instanceID: 36056}
    - Class: 114
      Script: {instanceID: 36128}
    - Class: 114
      Script: {instanceID: 36150}
    - Class: 114
      Script: {instanceID: 36168}
    - Class: 114
      Script: {instanceID: 36226}
    - Class: 114
      Script: {instanceID: 36378}
    - Class: 114
      Script: {instanceID: 36402}
    - Class: 114
      Script: {instanceID: 36594}
    - Class: 114
      Script: {instanceID: 36690}
    - Class: 114
      Script: {instanceID: 36892}
    - Class: 114
      Script: {instanceID: 37128}
    - Class: 114
      Script: {instanceID: 37176}
    - Class: 114
      Script: {instanceID: 37570}
    - Class: 114
      Script: {instanceID: 37650}
    - Class: 114
      Script: {instanceID: 37794}
    - Class: 114
      Script: {instanceID: 37864}
    - Class: 114
      Script: {instanceID: 37968}
    - Class: 114
      Script: {instanceID: 38124}
    - Class: 114
      Script: {instanceID: 38228}
    - Class: 114
      Script: {instanceID: 38282}
    - Class: 114
      Script: {instanceID: 38372}
    - Class: 114
      Script: {instanceID: 38414}
    - Class: 114
      Script: {instanceID: 38448}
    - Class: 114
      Script: {instanceID: 38630}
    - Class: 114
      Script: {instanceID: 39308}
    - Class: 114
      Script: {instanceID: 39560}
    - Class: 114
      Script: {instanceID: 39930}
    - Class: 114
      Script: {instanceID: 39962}
    - Class: 114
      Script: {instanceID: 40258}
    - Class: 114
      Script: {instanceID: 40594}
    - Class: 114
      Script: {instanceID: 40794}
    - Class: 114
      Script: {instanceID: 40796}
    - Class: 114
      Script: {instanceID: 40862}
    - Class: 114
      Script: {instanceID: 40884}
    - Class: 114
      Script: {instanceID: 40898}
    - Class: 114
      Script: {instanceID: 41048}
    - Class: 114
      Script: {instanceID: 41300}
    - Class: 114
      Script: {instanceID: 41368}
    - Class: 114
      Script: {instanceID: 41394}
    - Class: 114
      Script: {instanceID: 41466}
    - Class: 114
      Script: {instanceID: 41564}
    - Class: 114
      Script: {instanceID: 41712}
    - Class: 114
      Script: {instanceID: 42154}
    - Class: 114
      Script: {instanceID: 42472}
    - Class: 114
      Script: {instanceID: 42726}
    - Class: 114
      Script: {instanceID: 42838}
    - Class: 114
      Script: {instanceID: 42842}
    - Class: 114
      Script: {instanceID: 43010}
    - Class: 114
      Script: {instanceID: 43166}
    - Class: 114
      Script: {instanceID: 43994}
    - Class: 114
      Script: {instanceID: 44110}
    - Class: 114
      Script: {instanceID: 44180}
    - Class: 114
      Script: {instanceID: 44448}
    - Class: 114
      Script: {instanceID: 44698}
    - Class: 114
      Script: {instanceID: 44878}
    - Class: 114
      Script: {instanceID: 45602}
    - Class: 114
      Script: {instanceID: 45818}
    - Class: 114
      Script: {instanceID: 45882}
    - Class: 114
      Script: {instanceID: 46128}
    - Class: 114
      Script: {instanceID: 46616}
    - Class: 114
      Script: {instanceID: 46640}
    - Class: 114
      Script: {instanceID: 46704}
    - Class: 114
      Script: {instanceID: 46910}
    - Class: 114
      Script: {instanceID: 47034}
    - Class: 114
      Script: {instanceID: 47076}
    - Class: 114
      Script: {instanceID: 47134}
    - Class: 114
      Script: {instanceID: 47178}
    - Class: 114
      Script: {instanceID: 47334}
    - Class: 114
      Script: {instanceID: 47544}
    - Class: 114
      Script: {instanceID: 47902}
    - Class: 114
      Script: {instanceID: 48022}
    - Class: 114
      Script: {instanceID: 48168}
    - Class: 114
      Script: {instanceID: 48298}
    - Class: 114
      Script: {instanceID: 48358}
    - Class: 114
      Script: {instanceID: 48394}
    - Class: 114
      Script: {instanceID: 48482}
    - Class: 114
      Script: {instanceID: 48520}
    - Class: 114
      Script: {instanceID: 48576}
    - Class: 114
      Script: {instanceID: 48710}
    - Class: 114
      Script: {instanceID: 49036}
    - Class: 114
      Script: {instanceID: 49132}
    - Class: 114
      Script: {instanceID: 49312}
    - Class: 114
      Script: {instanceID: 49404}
    - Class: 114
      Script: {instanceID: 49636}
    - Class: 114
      Script: {instanceID: 207424}
    - Class: 114
      Script: {instanceID: 207434}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 135
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 169
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 198
      Script: {instanceID: 0}
    - Class: 199
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    - Class: 241
      Script: {instanceID: 0}
    - Class: 243
      Script: {instanceID: 0}
    - Class: 245
      Script: {instanceID: 0}
    - Class: 850595691
      Script: {instanceID: 0}
    Assets/_Game/Scenes/TestScene.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 54
      Script: {instanceID: 0}
    - Class: 64
      Script: {instanceID: 0}
    - Class: 65
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 82
      Script: {instanceID: 0}
    - Class: 83
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 1558}
    - Class: 114
      Script: {instanceID: 1560}
    - Class: 114
      Script: {instanceID: 1562}
    - Class: 114
      Script: {instanceID: 1584}
    - Class: 114
      Script: {instanceID: 1586}
    - Class: 114
      Script: {instanceID: 34866}
    - Class: 114
      Script: {instanceID: 34956}
    - Class: 114
      Script: {instanceID: 35084}
    - Class: 114
      Script: {instanceID: 35360}
    - Class: 114
      Script: {instanceID: 35384}
    - Class: 114
      Script: {instanceID: 35474}
    - Class: 114
      Script: {instanceID: 35914}
    - Class: 114
      Script: {instanceID: 36056}
    - Class: 114
      Script: {instanceID: 36150}
    - Class: 114
      Script: {instanceID: 36594}
    - Class: 114
      Script: {instanceID: 36690}
    - Class: 114
      Script: {instanceID: 36926}
    - Class: 114
      Script: {instanceID: 36940}
    - Class: 114
      Script: {instanceID: 37128}
    - Class: 114
      Script: {instanceID: 37148}
    - Class: 114
      Script: {instanceID: 37176}
    - Class: 114
      Script: {instanceID: 37356}
    - Class: 114
      Script: {instanceID: 37544}
    - Class: 114
      Script: {instanceID: 37554}
    - Class: 114
      Script: {instanceID: 37794}
    - Class: 114
      Script: {instanceID: 37928}
    - Class: 114
      Script: {instanceID: 38412}
    - Class: 114
      Script: {instanceID: 38432}
    - Class: 114
      Script: {instanceID: 38582}
    - Class: 114
      Script: {instanceID: 38630}
    - Class: 114
      Script: {instanceID: 38692}
    - Class: 114
      Script: {instanceID: 39272}
    - Class: 114
      Script: {instanceID: 39676}
    - Class: 114
      Script: {instanceID: 40410}
    - Class: 114
      Script: {instanceID: 40796}
    - Class: 114
      Script: {instanceID: 40884}
    - Class: 114
      Script: {instanceID: 41030}
    - Class: 114
      Script: {instanceID: 41086}
    - Class: 114
      Script: {instanceID: 41394}
    - Class: 114
      Script: {instanceID: 41698}
    - Class: 114
      Script: {instanceID: 42154}
    - Class: 114
      Script: {instanceID: 42438}
    - Class: 114
      Script: {instanceID: 42840}
    - Class: 114
      Script: {instanceID: 42900}
    - Class: 114
      Script: {instanceID: 42912}
    - Class: 114
      Script: {instanceID: 42926}
    - Class: 114
      Script: {instanceID: 42968}
    - Class: 114
      Script: {instanceID: 43010}
    - Class: 114
      Script: {instanceID: 43994}
    - Class: 114
      Script: {instanceID: 43998}
    - Class: 114
      Script: {instanceID: 44378}
    - Class: 114
      Script: {instanceID: 44448}
    - Class: 114
      Script: {instanceID: 44698}
    - Class: 114
      Script: {instanceID: 45044}
    - Class: 114
      Script: {instanceID: 45228}
    - Class: 114
      Script: {instanceID: 45278}
    - Class: 114
      Script: {instanceID: 45524}
    - Class: 114
      Script: {instanceID: 45602}
    - Class: 114
      Script: {instanceID: 45882}
    - Class: 114
      Script: {instanceID: 45970}
    - Class: 114
      Script: {instanceID: 46156}
    - Class: 114
      Script: {instanceID: 46354}
    - Class: 114
      Script: {instanceID: 46616}
    - Class: 114
      Script: {instanceID: 46774}
    - Class: 114
      Script: {instanceID: 46784}
    - Class: 114
      Script: {instanceID: 46806}
    - Class: 114
      Script: {instanceID: 46910}
    - Class: 114
      Script: {instanceID: 46950}
    - Class: 114
      Script: {instanceID: 47134}
    - Class: 114
      Script: {instanceID: 47178}
    - Class: 114
      Script: {instanceID: 47218}
    - Class: 114
      Script: {instanceID: 47764}
    - Class: 114
      Script: {instanceID: 47836}
    - Class: 114
      Script: {instanceID: 48168}
    - Class: 114
      Script: {instanceID: 48194}
    - Class: 114
      Script: {instanceID: 48298}
    - Class: 114
      Script: {instanceID: 48332}
    - Class: 114
      Script: {instanceID: 48350}
    - Class: 114
      Script: {instanceID: 48576}
    - Class: 114
      Script: {instanceID: 48580}
    - Class: 114
      Script: {instanceID: 48882}
    - Class: 114
      Script: {instanceID: 49356}
    - Class: 114
      Script: {instanceID: 49430}
    - Class: 114
      Script: {instanceID: 49816}
    - Class: 114
      Script: {instanceID: 50310}
    - Class: 114
      Script: {instanceID: 844586}
    - Class: 114
      Script: {instanceID: 844588}
    - Class: 114
      Script: {instanceID: 2886762}
    - Class: 114
      Script: {instanceID: 2886824}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 135
      Script: {instanceID: 0}
    - Class: 136
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 169
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    - Class: 241
      Script: {instanceID: 0}
    - Class: 243
      Script: {instanceID: 0}
    - Class: 245
      Script: {instanceID: 0}
    - Class: 258
      Script: {instanceID: 0}
    - Class: 850595691
      Script: {instanceID: 0}
  scriptHashData:
  - hash:
      serializedVersion: 2
      Hash: d31a7bcce8f59bc38b76dfdb1af89796
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: HDRenderPipelineAsset
  - hash:
      serializedVersion: 2
      Hash: fddc1d0815370c41972670aaf78cbc9a
    assemblyName: Unity.ProBuilder
    namespaceName: UnityEngine.ProBuilder
    className: ColorPalette
  - hash:
      serializedVersion: 2
      Hash: 36afd2b3383c61ddf671e7c29c69c388
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXInputMouseBinder
  - hash:
      serializedVersion: 2
      Hash: 39ce22f88c4c9c795740cdb54f992ad6
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXInputTouchBinder
  - hash:
      serializedVersion: 2
      Hash: fd16c80dca48d8411ca0cd0bd06eccf1
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition.Compositor
    className: AlphaInjection
  - hash:
      serializedVersion: 2
      Hash: b2d2a01a2a85d9ed12e80cb33a9a6044
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AnimationTrack
  - hash:
      serializedVersion: 2
      Hash: bf16f3f7cc6924018428428f7664a0d0
    assemblyName: Unity.ProBuilder
    namespaceName: UnityEngine.ProBuilder
    className: PolyShape
  - hash:
      serializedVersion: 2
      Hash: ef58977e7fbf203164eb1a3c92704d4b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Loot
  - hash:
      serializedVersion: 2
      Hash: 1e486c5546e595d0af504dbc11e23ee2
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: HDAdditionalLightData
  - hash:
      serializedVersion: 2
      Hash: 7e33f927e460d7482678cd48cda00e8f
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: WaterSurface
  - hash:
      serializedVersion: 2
      Hash: b98fedb1a2fd5133c25f2a0ec44ee650
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: ExponentialFog
  - hash:
      serializedVersion: 2
      Hash: e28f9588c77c6dc06f5b4c6be665b166
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InvDroppedStorageEquipment
  - hash:
      serializedVersion: 2
      Hash: dd424a4766fcf13ca9b8ec9a486a9fad
    assemblyName: Tayx.Graphy
    namespaceName: Tayx.Graphy.Ram
    className: G_RamGraph
  - hash:
      serializedVersion: 2
      Hash: 6c887afbbd5f29abb6af7f4d74af0f5a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: CurrencyManager
  - hash:
      serializedVersion: 2
      Hash: cf62aad97f6c2dd5d482e2509c6c9528
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextMeshProUGUI
  - hash:
      serializedVersion: 2
      Hash: 026a577b8baa11e6202524d5c176d148
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: VisualEffectControlClip
  - hash:
      serializedVersion: 2
      Hash: 18aa856c5087b08827f1a49c2e5b8d0f
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: ReflectionProxyVolumeComponent
  - hash:
      serializedVersion: 2
      Hash: ab146b9b456a0ac8917d892a3f2e3f4b
    assemblyName: Unity.ProBuilder
    namespaceName: UnityEngine.ProBuilder
    className: TriggerBehaviour
  - hash:
      serializedVersion: 2
      Hash: cacee3938068544fbcd91ed5fdbde46b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ItemUniqueId
  - hash:
      serializedVersion: 2
      Hash: b6a1d8e91c5ff6949ad6c62533254816
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ToolSelectionManager
  - hash:
      serializedVersion: 2
      Hash: 2c583ad0ac85d03e8700dd05fdcb46cb
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: Variables
  - hash:
      serializedVersion: 2
      Hash: ccd984859978bca9682ba8db1d7b302b
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: HDRPCameraBinder
  - hash:
      serializedVersion: 2
      Hash: 8afdb787bd7e013ac70515dbf35eea5f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ToolSelectionDebugButton
  - hash:
      serializedVersion: 2
      Hash: b72d9c057c0f9460abf04218f4cc4f0e
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerMessageBox
  - hash:
      serializedVersion: 2
      Hash: 65db5e65011bc98f12b0ce61b5850a49
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: HDProjectSettingsReadOnlyBase
  - hash:
      serializedVersion: 2
      Hash: 54dd1468eb4653be05515ccd3456d82d
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: PlayerStatus
  - hash:
      serializedVersion: 2
      Hash: 4ee522268a4a674c67ae84fed33c642a
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: ContactShadows
  - hash:
      serializedVersion: 2
      Hash: 9ebc0a3016506a0cbb7e0306dfcb9497
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: DictionaryAsset
  - hash:
      serializedVersion: 2
      Hash: e7e95903ca66d58e1a7cbd71f824e16d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SubMeshUI
  - hash:
      serializedVersion: 2
      Hash: 1cebbfbd4cd8fffb47ea9ed2bc50c53c
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: ChannelMixer
  - hash:
      serializedVersion: 2
      Hash: f3719694b97c1e1994cb2bb45d821483
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InvItemDropping
  - hash:
      serializedVersion: 2
      Hash: aa20cb5dc7af29fc3ffbd68880a0b762
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: GlobalIllumination
  - hash:
      serializedVersion: 2
      Hash: fb00a12b194c52eab5ee5921112c20d0
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ShopSystem
  - hash:
      serializedVersion: 2
      Hash: 06292e824f1ffafad0a336f180478ecd
    assemblyName: Tayx.Graphy
    namespaceName: Tayx.Graphy.Ram
    className: G_RamManager
  - hash:
      serializedVersion: 2
      Hash: 0c0b64d103fe555d830e8ca206abd12a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 576439e4ec96257377c83e3fb18a7d38
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: Tonemapping
  - hash:
      serializedVersion: 2
      Hash: febcfbf0a0d85e37808a3740e3c2c6fa
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: MarkerTrack
  - hash:
      serializedVersion: 2
      Hash: 855ca311b9a11c475f6933001c901353
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: DecalProjector
  - hash:
      serializedVersion: 2
      Hash: 4392bfff79efe0630144f16ea4d656ab
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerObjectList
  - hash:
      serializedVersion: 2
      Hash: 53093a800b1d9bde394b265153fd77e0
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ItemDropPrefab
  - hash:
      serializedVersion: 2
      Hash: 8b15e35eb98fc258ec2e839df7c3c9b0
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSliderValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 682dfc653b22feea53b38adb3bc66414
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalReceiver
  - hash:
      serializedVersion: 2
      Hash: bfa9d6dfbc3c74a843a3b197bdcc6639
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_Settings
  - hash:
      serializedVersion: 2
      Hash: db9b308769d19e1f9e161df0392c23ca
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTransformChildrenChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 9dd54ce33440072d6692a67e0c384ad1
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: GroupTrack
  - hash:
      serializedVersion: 2
      Hash: 06d43bd8b4fc61d15b37324289a91537
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: DebugFlyController
  - hash:
      serializedVersion: 2
      Hash: f669be52680c88846b2093fd41d1e06e
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 20a120e55ad3ab65556328d5fd8309dd
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Outline
  - hash:
      serializedVersion: 2
      Hash: 2deddcdd5f664164bb803747210cbba2
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerRow
  - hash:
      serializedVersion: 2
      Hash: b170173e1dd4b1a7200fa574e3debaf6
    assemblyName: Assembly-CSharp
    namespaceName: AudioSystem
    className: EnvironmentalAudioHandler
  - hash:
      serializedVersion: 2
      Hash: 9e0107dc4fdc71242787fcff8e2e956f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Helmet
  - hash:
      serializedVersion: 2
      Hash: 1362033cd491d66ad3cc206af29cc0de
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: Volume
  - hash:
      serializedVersion: 2
      Hash: da281dc879c7932e0c9669da88ae40c0
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerGroup
  - hash:
      serializedVersion: 2
      Hash: 84056876ce8b14176cdd7f82aaefd21a
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXVisibilityEventBinder
  - hash:
      serializedVersion: 2
      Hash: 9507f221948bbd40853cc0ec75374d07
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: LensFlareComponentSRP
  - hash:
      serializedVersion: 2
      Hash: 7b6ff0d6e9daa391f6d88106114bbeef
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: ScreenSpaceReflection
  - hash:
      serializedVersion: 2
      Hash: e9726f9e6abce168e0d5803c0aee3d06
    assemblyName: Assembly-CSharp
    namespaceName: AudioSystem
    className: AudioEventDefinition
  - hash:
      serializedVersion: 2
      Hash: 633dd67c473d23f1e064e25b2bdd7844
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ConsumableDefinition
  - hash:
      serializedVersion: 2
      Hash: f294bb42d237ff3f282e8652c9f7b475
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: SetSpawnTime
  - hash:
      serializedVersion: 2
      Hash: 0bd2044f31f948e8e457585f08846860
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXTerrainBinder
  - hash:
      serializedVersion: 2
      Hash: e6ee7dccba41ae87d10e3d922c6cb71c
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_InputField
  - hash:
      serializedVersion: 2
      Hash: 522f6865b4ec12522e602206785309f8
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: GlobalMessageListener
  - hash:
      serializedVersion: 2
      Hash: 53e329840e6d03aebec76623c3b054db
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Slider
  - hash:
      serializedVersion: 2
      Hash: e0c609ab909400e2ccecf4ff4069e13f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InvItemPickup
  - hash:
      serializedVersion: 2
      Hash: 2bdc2158e5cc1b9cb13efc3b2970b905
    assemblyName: Autodesk.Fbx.BuildTestAssets
    namespaceName: Autodesk.Fbx.BuildTests
    className: ExportEmptyFbx
  - hash:
      serializedVersion: 2
      Hash: ef19274111491b6b8bcc6fd227f656f5
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIndirectFloatField
  - hash:
      serializedVersion: 2
      Hash: ee34255e3e86289bde9bad5e07b84cbc
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollRectValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 0df1394d30cfd5625e192150dcff771a
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: WaterSurfaceTrack
  - hash:
      serializedVersion: 2
      Hash: cad1f2432d1fbcbaa6873628003ba3fe
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: PaniniProjection
  - hash:
      serializedVersion: 2
      Hash: a0c40853474329add703b2bd7a55fcab
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: ShadowsMidtonesHighlights
  - hash:
      serializedVersion: 2
      Hash: dd9dda1dfd39e605c49e72003342ef5d
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Image
  - hash:
      serializedVersion: 2
      Hash: d3287028357363d4580bb5f6b17fdd3c
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: LiftGammaGain
  - hash:
      serializedVersion: 2
      Hash: 66496b917404bcb20f6bf50861c1adc6
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolumeBakingSet
  - hash:
      serializedVersion: 2
      Hash: dbe4f05d59170430398f6453d7253fe5
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerWidget
  - hash:
      serializedVersion: 2
      Hash: 245dfbaa1bff635366f81c301dde05a9
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: 
    className: ColorCheckerTool
  - hash:
      serializedVersion: 2
      Hash: 6d971045f8e67def7430d0b6f3cb0700
    assemblyName: Unity.RenderPipelines.GPUDriven.Runtime
    namespaceName: UnityEngine.Rendering
    className: DisallowSmallMeshCulling
  - hash:
      serializedVersion: 2
      Hash: 7bdff9e91edfd9600f92a185def7e34a
    assemblyName: Unity.VisualScripting.Flow
    namespaceName: Unity.VisualScripting
    className: ScriptMachine
  - hash:
      serializedVersion: 2
      Hash: 0a3f328f3ea906bbbcf794f4c27ee59d
    assemblyName: Unity.VisualScripting.Flow
    namespaceName: Unity.VisualScripting
    className: ScriptGraphAsset
  - hash:
      serializedVersion: 2
      Hash: 33f160a5c07e580c29aba2bd1e4db811
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: HorizontalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: de8e34fffe62b77ca47bae273d0414ba
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ObsoleteProbeVolumeAsset
  - hash:
      serializedVersion: 2
      Hash: 63eb44e4cc385f1ca59a6574bd7e417d
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ToolModelManager
  - hash:
      serializedVersion: 2
      Hash: 4958cca528e79392a2b50aa5acb1460b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: CrosshairManager
  - hash:
      serializedVersion: 2
      Hash: cb57574ce2602aef370d7c3e094363d0
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: BatteryController
  - hash:
      serializedVersion: 2
      Hash: 1bc6ff8d02d2d68019aa6a028f8d409d
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerUpMessageListener
  - hash:
      serializedVersion: 2
      Hash: 569b008d5e16ac2b150193fb0f8bdbdc
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: IndirectLightingController
  - hash:
      serializedVersion: 2
      Hash: e987953190eb4c12cf19df790566dc2c
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: EventSystem
  - hash:
      serializedVersion: 2
      Hash: 03425e8898b386ef5d1d23385b7cd353
    assemblyName: Unity.RenderPipelines.Core.Samples.Runtime
    namespaceName: 
    className: SamplesLinkLightToEmissive
  - hash:
      serializedVersion: 2
      Hash: 35796d3e4b54c3625fc950fe28032487
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: Fog
  - hash:
      serializedVersion: 2
      Hash: 3f7fd8381bb04913fb787b19a7ae65ad
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: IncrementStripIndexOnStart
  - hash:
      serializedVersion: 2
      Hash: a8d0629dc5de07a3f8480c728dee1017
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: VolumetricFog
  - hash:
      serializedVersion: 2
      Hash: aef0d0885a03f8f85e8bff9026f542df
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXTransformBinder
  - hash:
      serializedVersion: 2
      Hash: 33656f83bb0079184b2206a69690ec46
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: AspectRatioFitter
  - hash:
      serializedVersion: 2
      Hash: da5a949a0bbd2ff91bc51c5805c2c41f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseUpAsButtonMessageListener
  - hash:
      serializedVersion: 2
      Hash: 9104447b683bf70406fa01f12ecb564a
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: StandaloneInputModule
  - hash:
      serializedVersion: 2
      Hash: ea5e0cb2f62c5bafc3dfb7bcdc9595e1
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: DebugUpdater
  - hash:
      serializedVersion: 2
      Hash: b14e186823458f549289789cccbb77ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerDownMessageListener
  - hash:
      serializedVersion: 2
      Hash: ee5e9776f90de2c09bc590c22a1f7062
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ItemDebugger
  - hash:
      serializedVersion: 2
      Hash: 67272c9c30c50b68ef1ce72ac0191c57
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMoveMessageListener
  - hash:
      serializedVersion: 2
      Hash: b082367348139cf3fbfcb5a1daf10bd4
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Item
  - hash:
      serializedVersion: 2
      Hash: 2197973ab791d2613ee0a254db975e25
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InteractiveWorldScreenEditor
  - hash:
      serializedVersion: 2
      Hash: cabf95f3755c30f1b4ff715323a3c736
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: PauseMenuManager
  - hash:
      serializedVersion: 2
      Hash: 39dd0e436997c150fe0ae3bb86ee792b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: DevSceneInitializer
  - hash:
      serializedVersion: 2
      Hash: 6b2b20d5352b9f1294595326f54bd3c2
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerObjectPopupField
  - hash:
      serializedVersion: 2
      Hash: 915204dac8acad99f076d6e72ada0a0f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Button
  - hash:
      serializedVersion: 2
      Hash: 52f6166572718025ef675e1e3546e767
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: VisualEnvironment
  - hash:
      serializedVersion: 2
      Hash: 2a16e929534705cc91f66500a3837689
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: VolumetricClouds
  - hash:
      serializedVersion: 2
      Hash: c0a2b4d13675f08bfb211e044c334e68
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIntField
  - hash:
      serializedVersion: 2
      Hash: 090b4014813624206b1713d7decd7b0d
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXInputKeyBinder
  - hash:
      serializedVersion: 2
      Hash: 7fcf31e7fa226e7e1e7dbeed5c64d9b2
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: CustomPassVolume
  - hash:
      serializedVersion: 2
      Hash: afb7b65d28d5a7423e69a1b076be0064
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: PositionAsUV1
  - hash:
      serializedVersion: 2
      Hash: e11b2ee3701c4f75dfc6454a7afcd40b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: StashSystem
  - hash:
      serializedVersion: 2
      Hash: 4728e3d21cc2a12e391e71ec9b7eed0b
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalTrack
  - hash:
      serializedVersion: 2
      Hash: 8cbf9f9b3c5693506e7d7cba105666f7
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: MenuCameraController
  - hash:
      serializedVersion: 2
      Hash: b30c8635462e66ac20f4241106119f77
    assemblyName: Unity.VisualScripting.State
    namespaceName: Unity.VisualScripting
    className: StateMachine
  - hash:
      serializedVersion: 2
      Hash: 67d8697ed198d0033bd79cc8af02169c
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: BakeryLightmapGroup
  - hash:
      serializedVersion: 2
      Hash: 3b5263a98bcc71d9a9d1d78a31b34ced
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SelectionCaret
  - hash:
      serializedVersion: 2
      Hash: d41ab9b5ea92474f9c0a6937a0607ce1
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ContentSizeFitter
  - hash:
      serializedVersion: 2
      Hash: 0f6b6107168e347e854c0107aa8cb9fb
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerEnumHistory
  - hash:
      serializedVersion: 2
      Hash: 51b80d3734ea86b38e3101db98029e15
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AnimationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 7d026ae582dc02c5edc3fc23b8c03ff7
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector3
  - hash:
      serializedVersion: 2
      Hash: cf9b62ba05d6d0891bf3dc7ad87bd0ee
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: DensityVolume
  - hash:
      serializedVersion: 2
      Hash: b72839eb6e4350778dc879d98832aa2b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerHBox
  - hash:
      serializedVersion: 2
      Hash: 2bd8fc0792a4bc563121b050a66196a5
    assemblyName: Tayx.Graphy
    namespaceName: Tayx.Graphy.Audio
    className: G_AudioText
  - hash:
      serializedVersion: 2
      Hash: 67ba98f8a8977f3e60e5f0e049923033
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerColor
  - hash:
      serializedVersion: 2
      Hash: 0d33ddde90dba73b2112cd05d944362b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: NotificationManager
  - hash:
      serializedVersion: 2
      Hash: 7bb32d6e4c7614a97d9723ce7e26588c
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: BasicPlayableBehaviour
  - hash:
      serializedVersion: 2
      Hash: d0cdd713f9bf4e6a625d9e7804deca42
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: EventTrigger
  - hash:
      serializedVersion: 2
      Hash: ea7feb54a409c67049a23207c417ee02
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: SubSurfaceScattering
  - hash:
      serializedVersion: 2
      Hash: 2ccb98a24d51cf9f613cd9cfdc894cf2
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: EquipmentManager
  - hash:
      serializedVersion: 2
      Hash: 5b26d0bbd0d4bcf9cf227f9aef53f276
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: WaterPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: b98d37789b79ff80bec70bc049d79aa9
    assemblyName: Tayx.Graphy
    namespaceName: Tayx.Graphy.Fps
    className: G_FpsManager
  - hash:
      serializedVersion: 2
      Hash: 1ed0a59ccfb7fbbd707725e5615ff85f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: UIPanelManager
  - hash:
      serializedVersion: 2
      Hash: 147a84d1a8f1960e6368513ecb824084
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: ScreenSpaceAmbientOcclusion
  - hash:
      serializedVersion: 2
      Hash: c7ab8be5c93cba2d6f4264bfeca8ae62
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition.Compositor
    className: CompositionManager
  - hash:
      serializedVersion: 2
      Hash: cd3c4c5489f2338a87cf16b315540c80
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ToolSelectionController
  - hash:
      serializedVersion: 2
      Hash: e88d71ef93aba4e7faf243430f6a10ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: MacroScriptableObject
  - hash:
      serializedVersion: 2
      Hash: faccd05cbe8b025f92660e27a727fe62
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXPositionBinder
  - hash:
      serializedVersion: 2
      Hash: c9ea7fc6e584a91f99e24369a904c607
    assemblyName: Unity.ProBuilder
    namespaceName: UnityEngine.ProBuilder
    className: Entity
  - hash:
      serializedVersion: 2
      Hash: 4f353524ce6564e40764f1ea080b2d85
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerStay2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: c0eedab726922bda7657d5bbb39bae23
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: ComputeMaterialLibrary
  - hash:
      serializedVersion: 2
      Hash: bb3c46ea3d7902a7056b95a948b6bddd
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: PredictionVisualizer
  - hash:
      serializedVersion: 2
      Hash: 02a47340661d2ea7c6d30e292cfef403
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnToggleValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: a3901985eb076c1c9b7562fca82ca3e0
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalAsset
  - hash:
      serializedVersion: 2
      Hash: af2a83f2d3f7aa4ae00c902794c0c5b6
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ProceduralPipeGenerator
  - hash:
      serializedVersion: 2
      Hash: f295412bc00b58b9095a71e9764cc886
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AudioTrack
  - hash:
      serializedVersion: 2
      Hash: 99ee9ebd61243f7cd72023388979c845
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InvDragAndDropManager
  - hash:
      serializedVersion: 2
      Hash: 7feddafb4025584c0413fa5ada6da112
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: BakeryVolumeReceiver
  - hash:
      serializedVersion: 2
      Hash: 8e6dc3d79712ca4934f6d50c80f010b1
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SpriteAnimator
  - hash:
      serializedVersion: 2
      Hash: c6369b4cc3be620d6c9300e920bc3d52
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: VariablesAsset
  - hash:
      serializedVersion: 2
      Hash: cf32d98954e63b8e30e9334b152e9b8e
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: BaseInput
  - hash:
      serializedVersion: 2
      Hash: 92e44eea49cb6459087f58ae28c75d31
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: EnhancedPipeGenerator
  - hash:
      serializedVersion: 2
      Hash: 46b2115e2aa3befb83d200bbcdaad575
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: WorldItemManager
  - hash:
      serializedVersion: 2
      Hash: ec3157dc31792bbbd04ac935e0e2b57f
    assemblyName: Assembly-CSharp
    namespaceName: KinematicCharacterController.FPS
    className: FPSCharacterController
  - hash:
      serializedVersion: 2
      Hash: 33d637ee6254b20bb9f5a5f8114bc750
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: LensDistortion
  - hash:
      serializedVersion: 2
      Hash: 32e45e9321b32487564fbc9be7df51eb
    assemblyName: Unity.ProBuilder
    namespaceName: UnityEngine.ProBuilder
    className: ProBuilderMesh
  - hash:
      serializedVersion: 2
      Hash: df79ccb4cd3f3f8a5449fcd589774b35
    assemblyName: Unity.ProBuilder
    namespaceName: UnityEngine.ProBuilder
    className: PreferenceDictionary
  - hash:
      serializedVersion: 2
      Hash: 28fbce76948314ffe14796155f2fdead
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Manual
  - hash:
      serializedVersion: 2
      Hash: b24bae4511d404331640e62f8b8c2ed0
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolumesOptions
  - hash:
      serializedVersion: 2
      Hash: 2a2eef56932c95206cbf2fb724fb40af
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXMultiplePositionBinder
  - hash:
      serializedVersion: 2
      Hash: 8dff8e9853297d341afa0c11f9091b59
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: LayoutElement
  - hash:
      serializedVersion: 2
      Hash: 1321993db8249e1e05e1e0492b2bbdc3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBecameVisibleMessageListener
  - hash:
      serializedVersion: 2
      Hash: a90ef02b2b3b090b156308ca986c67a2
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: ftGlobalStorage
  - hash:
      serializedVersion: 2
      Hash: c025109c6e949c725da4d864f8ca7807
    assemblyName: Tayx.Graphy
    namespaceName: Tayx.Graphy.Ram
    className: G_RamMonitor
  - hash:
      serializedVersion: 2
      Hash: d90629714fa6b0145fcaffc87f5c6559
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnInputFieldValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: b6307dfc54cca9ba44f47185c578b0ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: fb04b085a61c1503352b3bdca109c4da
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ClimbingSystemActivator
  - hash:
      serializedVersion: 2
      Hash: fe0a072dbdcbca34d5003c5299de3aaf
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: SceneRenderPipeline
  - hash:
      serializedVersion: 2
      Hash: 9bf2b7539cfd13f5ecc7c1fc38be9b4a
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering
    className: AdditionalShadowData
  - hash:
      serializedVersion: 2
      Hash: 8dc40a01667ca02b3354a4e7a61be082
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: GridLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 9ed84ccc7f822799f8d08159e2a3e77d
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXMouseEventBinder
  - hash:
      serializedVersion: 2
      Hash: bf0842d88681628b29beb00b5e2ab785
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDeselectMessageListener
  - hash:
      serializedVersion: 2
      Hash: 594d6ec0e6eecc18d4f02bb49844180f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InvUI
  - hash:
      serializedVersion: 2
      Hash: 4878e735441f4041539853759559c86f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: VerticalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: e0cf060d3a0d1afae0c661ac42606ab8
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolume
  - hash:
      serializedVersion: 2
      Hash: 198d434ffcb2023a853247a51b5e8fa3
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ItemRegistry
  - hash:
      serializedVersion: 2
      Hash: 37b313f4e8b7842a765af21d06776f97
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: ftLocalStorage
  - hash:
      serializedVersion: 2
      Hash: 61d319b1d8fd2642e38d556cd5e2c9c9
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: BakeryVolumeTrigger
  - hash:
      serializedVersion: 2
      Hash: db5a43824d144e03e041b467fed45c50
    assemblyName: Unity.RenderPipelines.Core.Samples.Runtime
    namespaceName: 
    className: AlignSceneView
  - hash:
      serializedVersion: 2
      Hash: 715f7928b39881049ce8c46350cb8681
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTransformParentChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: d781f6333ee60fcacaa19092e72704b9
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: BakeryPointLight
  - hash:
      serializedVersion: 2
      Hash: a020c44a85e9b4d501ba7462f33a2f5a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: StartMenuManager
  - hash:
      serializedVersion: 2
      Hash: 81a800f7a34f3ddc66a9107c8f2fd833
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: FlarePool
  - hash:
      serializedVersion: 2
      Hash: 113e54f67456d0e7754c0598ee2a6dd4
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolumePerSceneData
  - hash:
      serializedVersion: 2
      Hash: 28fd3d045acc780719a17f02073eb448
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: InputField
  - hash:
      serializedVersion: 2
      Hash: 4001f3727b45ffa51ecc45ba969b4108
    assemblyName: Unity.Recorder
    namespaceName: UnityEngine.Recorder
    className: RecorderBindings
  - hash:
      serializedVersion: 2
      Hash: 831a07ec3647706fc718cb04ae39f2c9
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXPropertyBinder
  - hash:
      serializedVersion: 2
      Hash: c833d8ddc320fe5153f6a88cef23c858
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerObject
  - hash:
      serializedVersion: 2
      Hash: b807216d90fedb10e3ab12ae40f5e883
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: Vignette
  - hash:
      serializedVersion: 2
      Hash: 47f78e94e3df8d612cefa1307a19e2ce
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerValue
  - hash:
      serializedVersion: 2
      Hash: 4c0d63dc9d7b30f3c30a8d5f9f433eca
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: StaticLightingSky
  - hash:
      serializedVersion: 2
      Hash: b091df1d5a72650c0ea012ab3040b06b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerRenderingLayerField
  - hash:
      serializedVersion: 2
      Hash: 7277b2ff1fcef431e3c78fd0e5b367cf
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition.Compositor
    className: CompositionProfile
  - hash:
      serializedVersion: 2
      Hash: 87fdcfa171e95fc203f55efdf039bc40
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: VoidRescueSystem
  - hash:
      serializedVersion: 2
      Hash: 4664f8b684501c60a96db6c816d27075
    assemblyName: Assembly-CSharp
    namespaceName: AudioSystem
    className: GlobalAudioManager
  - hash:
      serializedVersion: 2
      Hash: 4fcf0bb4aff1d14a1dfcdedc4b07bd05
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: TouchInputModule
  - hash:
      serializedVersion: 2
      Hash: 8d7ab1303b1c14da5fdd62bf96ca6e89
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: FreeCamera
  - hash:
      serializedVersion: 2
      Hash: 34bcbd4b9a8d1ee94b65669777a5e8b4
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXInputButtonBinder
  - hash:
      serializedVersion: 2
      Hash: 8703b6ae2b0eefd9dafbc44d8d333fb1
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Shadow
  - hash:
      serializedVersion: 2
      Hash: 2be3311c714ed7d9f0acfbd89c026485
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXVelocityBinder
  - hash:
      serializedVersion: 2
      Hash: 1ab136900c0edaeeb49424c852595030
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: RawImage
  - hash:
      serializedVersion: 2
      Hash: 1d4567c66d87ac6bae13602d2bb3357b
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: ScreenSpaceRefraction
  - hash:
      serializedVersion: 2
      Hash: 1ce8e0f3a03bec598606a71df8746780
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: HDRenderPipelineGlobalSettings
  - hash:
      serializedVersion: 2
      Hash: 369610fad7f51ed8181e8df2dc481a96
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: WhiteBalance
  - hash:
      serializedVersion: 2
      Hash: 7053e305cdc1eb560f4c3be49d3b5136
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: PhysicsRaycaster
  - hash:
      serializedVersion: 2
      Hash: e5f9868e485ac5248f96a3be584a5e4a
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ControlPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 810e3c228b295cc60c4de2cdd94b18cb
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SpriteAsset
  - hash:
      serializedVersion: 2
      Hash: f2098fcd8ee983a05ec192f63904298f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnEndDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 953fad5c5ab1e7bb889420c434db93fe
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerFloatField
  - hash:
      serializedVersion: 2
      Hash: a5b008dcf3e82b568c0b84066aef8fc9
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: HDShadowSettings
  - hash:
      serializedVersion: 2
      Hash: b43f882d89edc4d94ad24a7fb3f35e92
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: ColorCurves
  - hash:
      serializedVersion: 2
      Hash: a908b098f2284663c0b9130e88844772
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerCanvas
  - hash:
      serializedVersion: 2
      Hash: 75acf8febb34de25a5336e7d33987c81
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: AutoSafetySystem
  - hash:
      serializedVersion: 2
      Hash: c4b85a67513b4f0cf7b5fb2d5ded04b0
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 711ba963e2b245a25de71beeaeeeb658
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerFoldout
  - hash:
      serializedVersion: 2
      Hash: 02459b88dbcf8448a9f31173782862b7
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: DepthOfField
  - hash:
      serializedVersion: 2
      Hash: 444b3236a20a1be2176f6173db6b8387
    assemblyName: Tayx.Graphy
    namespaceName: Tayx.Graphy.Audio
    className: G_AudioGraph
  - hash:
      serializedVersion: 2
      Hash: b2e8fc44f9fe7b5163b8dda3f3bd45d6
    assemblyName: Unity.Formats.Fbx.Runtime
    namespaceName: UnityEngine.Formats.Fbx.Exporter
    className: FbxPrefab
  - hash:
      serializedVersion: 2
      Hash: 9f355e210e7950e47ad1391c7681435b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerEnumField
  - hash:
      serializedVersion: 2
      Hash: 43ebd945b04bced376c791411cf5a289
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSelectMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5c9a3b406639c0494357de6bd29f9e1b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ForceToolSelectionDisplay
  - hash:
      serializedVersion: 2
      Hash: a35c9841208324f0a79362b885a00fee
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: HighQualityLineRenderingVolumeComponent
  - hash:
      serializedVersion: 2
      Hash: 2a35804323d29a1b6fb6df72a66c4957
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition.Compositor
    className: ChromaKeying
  - hash:
      serializedVersion: 2
      Hash: e3e8664035357fc3cb2713a5c719e060
    assemblyName: Unity.ProBuilder
    namespaceName: UnityEngine.ProBuilder
    className: ColliderBehaviour
  - hash:
      serializedVersion: 2
      Hash: a9f0153fc675e1f4ade818b83e4ff0c1
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: VolumeComponent
  - hash:
      serializedVersion: 2
      Hash: 209c1378f23c45375951c1715943159f
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXTriggerEventBinder
  - hash:
      serializedVersion: 2
      Hash: 13f262cc0741566a7ab72682984bc3e1
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SubMesh
  - hash:
      serializedVersion: 2
      Hash: 9984219f506ac6175f63d86d3d1a3b17
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ToggleObjectTrigger
  - hash:
      serializedVersion: 2
      Hash: 9a8b0ec145fb3547c7601d69192e75c8
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: MoneyCounterAnimation
  - hash:
      serializedVersion: 2
      Hash: 0f5a086d4d028363983ba6ca30a01397
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseOverMessageListener
  - hash:
      serializedVersion: 2
      Hash: 86e3db950c649c325827f8986ce15b13
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition.Compositor
    className: AdditionalCompositorData
  - hash:
      serializedVersion: 2
      Hash: 6c9efa1cb930143fcd1c39ae4611f722
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: TraderInventory
  - hash:
      serializedVersion: 2
      Hash: 868acd1682d58b5c58062b33a6b318b7
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: WaterRendering
  - hash:
      serializedVersion: 2
      Hash: e23d2210701e34e6850023cabe554c1f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnInputFieldEndEditMessageListener
  - hash:
      serializedVersion: 2
      Hash: a66c8b45deb769379021d69267d2a1ef
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InvItemSplitter
  - hash:
      serializedVersion: 2
      Hash: a624e8926762e588c5e4bdc7ed77080f
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXHierarchyAttributeMapBinder
  - hash:
      serializedVersion: 2
      Hash: ee617627107282672d01b8b65b0d6226
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: GridRenderer
  - hash:
      serializedVersion: 2
      Hash: 9db6ee95117846ae652f79a8a399acab
    assemblyName: Unity.RenderPipelines.Core.Samples.Runtime
    namespaceName: 
    className: GetSampleInfos
  - hash:
      serializedVersion: 2
      Hash: 14e97e45c276570c526f5af4adddc609
    assemblyName: Assembly-CSharp
    namespaceName: KinematicCharacterController
    className: KCCSettings
  - hash:
      serializedVersion: 2
      Hash: 4c16b48c041225942500eb86b895320a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: WakeUpPoint
  - hash:
      serializedVersion: 2
      Hash: a90f6a00b23520788be7d89ee3b0773d
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Scrollbar
  - hash:
      serializedVersion: 2
      Hash: 72f5beca70f136b7609b8cecadf241ce
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: RecursiveRendering
  - hash:
      serializedVersion: 2
      Hash: e0489cb02bdeee1fc85b0d1a0dbcf838
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXEnabledBinder
  - hash:
      serializedVersion: 2
      Hash: 8ed1980cbef176651b026ecb679ad294
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXRigidBodyCollisionEventBinder
  - hash:
      serializedVersion: 2
      Hash: f5df081962c06a171f380eb236cd1f3c
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVBox
  - hash:
      serializedVersion: 2
      Hash: e8fd74e57e72e8e4ef28792881adb864
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerUIntField
  - hash:
      serializedVersion: 2
      Hash: a2381973373bbb4e08a454b5a1c579b9
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: DeathManager
  - hash:
      serializedVersion: 2
      Hash: 58570e231c93c2f96918856759b81286
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerPanel
  - hash:
      serializedVersion: 2
      Hash: 7834494bac48da8b2e33935febd85099
    assemblyName: Tayx.Graphy
    namespaceName: Tayx.Graphy.Ram
    className: G_RamText
  - hash:
      serializedVersion: 2
      Hash: 3b149fcb1a745d69b75ffd066b4b5425
    assemblyName: Assembly-CSharp
    namespaceName: KinematicCharacterController
    className: KinematicCharacterMotor
  - hash:
      serializedVersion: 2
      Hash: 6017867a5b9309a9df8e5f8ab1b0c284
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RagdollTumbleSystem
  - hash:
      serializedVersion: 2
      Hash: e8d390bd9ecacc453a7500b90d0c0292
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: Physics2DRaycaster
  - hash:
      serializedVersion: 2
      Hash: 840a79762645d3571448a1547141a12c
    assemblyName: Tayx.Graphy
    namespaceName: Tayx.Graphy
    className: GraphyDebugger
  - hash:
      serializedVersion: 2
      Hash: a89b0448af4e1cd103a77f9fec0a961f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSubmitMessageListener
  - hash:
      serializedVersion: 2
      Hash: f2573bd1ab40ea8dbb27ba5bdbebb6b0
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXSphereBinder
  - hash:
      serializedVersion: 2
      Hash: 142e5574dff48485af6266f7ee67a66a
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: BakerySkyLight
  - hash:
      serializedVersion: 2
      Hash: d9f8ac82b6a20281cd7efea199c0d1fe
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: Bloom
  - hash:
      serializedVersion: 2
      Hash: 5a4aa2850fe11587f47e34c270c7fd22
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: GraphicRaycaster
  - hash:
      serializedVersion: 2
      Hash: 292b7e37d93a1d66a29e1451587dba86
    assemblyName: Assembly-CSharp
    namespaceName: AudioSystem
    className: PlayerAudioHandler
  - hash:
      serializedVersion: 2
      Hash: ae96bba85c62abf5cd629274892c1823
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: VolumeTestScene2
  - hash:
      serializedVersion: 2
      Hash: e6f8333cc8a800e2866870a5ef7efc35
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnControllerColliderHitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 1ce63d97d1ca25cba2d91d5f32fd5f79
    assemblyName: Unity.VisualScripting.State
    namespaceName: Unity.VisualScripting
    className: StateGraphAsset
  - hash:
      serializedVersion: 2
      Hash: 8045ced5ee2a46e2daf125bca55c2fad
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: MicroShadowing
  - hash:
      serializedVersion: 2
      Hash: 568292fb7ea6c4f7a667e5ff76bc7e85
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseUpMessageListener
  - hash:
      serializedVersion: 2
      Hash: 55060a7bf226ac385ce19ebd450f199f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UIElements
    className: PanelRaycaster
  - hash:
      serializedVersion: 2
      Hash: 7e12302f66e3c8ed5bcfea4ce65fccea
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityMessageListener
  - hash:
      serializedVersion: 2
      Hash: bed1d0c9a094cf0b0cd5004450b15031
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: BakeryLightmappedPrefab
  - hash:
      serializedVersion: 2
      Hash: bd97bdfa28ea07e50fdde211b59af203
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: ChromaticAberration
  - hash:
      serializedVersion: 2
      Hash: fde5bf5f3d66238db6a2ee7da4ef4a19
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InteractableObject
  - hash:
      serializedVersion: 2
      Hash: cb19700b928c8ea34e94f1489313e69c
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ClimbingRopeSystem
  - hash:
      serializedVersion: 2
      Hash: 6d94138b60a9299798dda96c1ee6725f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBeginDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8e0fc96fa85d5f45718225025b01474d
    assemblyName: Unity.ProGrids
    namespaceName: UnityEngine.ProGrids
    className: IgnoreSnap
  - hash:
      serializedVersion: 2
      Hash: 6135eccd8b6c7246bff90d1d990dfbc5
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: MoneyCounterInitializer
  - hash:
      serializedVersion: 2
      Hash: 1553f209ae0b2d5d3a35685fca55f9c3
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_ScrollbarEventHandler
  - hash:
      serializedVersion: 2
      Hash: 8bf800a764665b085fe469e8aea7f167
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: db6118e60e373f9c361a4eac322c563c
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: GradientSky
  - hash:
      serializedVersion: 2
      Hash: a0f1b77ef41476bd02f3a732f2d579bb
    assemblyName: Tayx.Graphy
    namespaceName: Tayx.Graphy.Fps
    className: G_FpsMonitor
  - hash:
      serializedVersion: 2
      Hash: f854af586011dd194574f89546642e29
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: d4c7cb9e46d192844a5b295d6a3f8dc5
    assemblyName: Unity.RenderPipelines.GPUDriven.Runtime
    namespaceName: UnityEngine.Rendering
    className: DisallowGPUDrivenRendering
  - hash:
      serializedVersion: 2
      Hash: 2ed6a1ce4df5362aeb2c5078bd8cb16b
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: BakeryDirectLight
  - hash:
      serializedVersion: 2
      Hash: 10ffd5579f941e0f4946756d18f1a9df
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerValueTuple
  - hash:
      serializedVersion: 2
      Hash: c78ed13f6b5a632d70d4f73f56ad81f2
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: VariablesSaver
  - hash:
      serializedVersion: 2
      Hash: 1a680d9a08de79b0d2c779e78c9843b4
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: RectMask2D
  - hash:
      serializedVersion: 2
      Hash: 17fd798e1149923da7238327d6f01c66
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Bag
  - hash:
      serializedVersion: 2
      Hash: 0530cef14419a5faa2e589f926dc7676
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: WaterDecal
  - hash:
      serializedVersion: 2
      Hash: 4bbc251a5e2cca425c313a0f3c3fde65
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: HDAdditionalMeshRendererSettings
  - hash:
      serializedVersion: 2
      Hash: e8ac0e02865bb68f46e0eeea62ff64fd
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: FallDamageSystem
  - hash:
      serializedVersion: 2
      Hash: 73ce6bd2557b3de2ba37526255e1ec27
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: VolumetricLightingController
  - hash:
      serializedVersion: 2
      Hash: 4bb8dde37a6865c0f9a229c9eb9a6cc9
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXInputAxisBinder
  - hash:
      serializedVersion: 2
      Hash: bd1895cb3df1aacd619543dd82f5c20a
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: HDAdditionalCameraData
  - hash:
      serializedVersion: 2
      Hash: 9258b26275630971c0268e8afa3c666d
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: DiffusionProfileList
  - hash:
      serializedVersion: 2
      Hash: f5fe6349105d28656eb60325a1be3509
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: WaterFoamGenerator
  - hash:
      serializedVersion: 2
      Hash: d452694764f00b7901622b2f94e111d1
    assemblyName: Assembly-CSharp
    namespaceName: AudioSystem
    className: UIAudioHandler
  - hash:
      serializedVersion: 2
      Hash: 3b9a45180ffcbe344c6f991c7c817bd2
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: FilmGrain
  - hash:
      serializedVersion: 2
      Hash: 96e7bcfd2d072992dab64ac44058b930
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCancelMessageListener
  - hash:
      serializedVersion: 2
      Hash: 415316b9bbc4e339c901a46503e2790b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: VolumeProfile
  - hash:
      serializedVersion: 2
      Hash: db36d5b90ccc467057aa75ac7a157412
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: BakerySectorCapture
  - hash:
      serializedVersion: 2
      Hash: cd8fac98eb8d947159707144ab85d060
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: CloudLayer
  - hash:
      serializedVersion: 2
      Hash: 24af424a437762b0c98a2238a41b2825
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerButton
  - hash:
      serializedVersion: 2
      Hash: d8e48ccd0b365433e7d42dfc3fd6ee78
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: MirrorOrbitCamera
  - hash:
      serializedVersion: 2
      Hash: ff76cf6cf52b4db7b95af6ffadca9288
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalEmitter
  - hash:
      serializedVersion: 2
      Hash: 008dde878e93da6aab246881a01f4c3e
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SettingsVideoManager
  - hash:
      serializedVersion: 2
      Hash: d7c8092511063e0dae8106bfc8ed63dd
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollMessageListener
  - hash:
      serializedVersion: 2
      Hash: 0689a1779568727c89c3997ba541048c
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: PlayerDebugDisplay
  - hash:
      serializedVersion: 2
      Hash: 0b63f6d3335e11939ce8790b904c8691
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ActivationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: ec6cf673540b2d4e42c612611eeafe4d
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerStayMessageListener
  - hash:
      serializedVersion: 2
      Hash: b19cc9d531ebed8e454c4c3e6eee7791
    assemblyName: Tayx.Graphy
    namespaceName: Tayx.Graphy.Audio
    className: G_AudioMonitor
  - hash:
      serializedVersion: 2
      Hash: dfac8bcc2c36764fb81cd513e2a24d64
    assemblyName: Tayx.Graphy
    namespaceName: Tayx.Graphy.Advanced
    className: G_AdvancedData
  - hash:
      serializedVersion: 2
      Hash: 8f60f2b52cfa5f9146ac0cce68970788
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXPlaneBinder
  - hash:
      serializedVersion: 2
      Hash: dc541f0c41945b5c6e682d5669713764
    assemblyName: Tayx.Graphy
    namespaceName: Tayx.Graphy
    className: GraphyManager
  - hash:
      serializedVersion: 2
      Hash: 8fe6d4f095ce838f5396d1867140a9b6
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: CameraSwitcher
  - hash:
      serializedVersion: 2
      Hash: 7976c8cb15ba9e665c907c4544b143dd
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SettingsAudioManager
  - hash:
      serializedVersion: 2
      Hash: 5cc3873f49597406d60e9f87751aa28f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: VoidRescueCapsuleController
  - hash:
      serializedVersion: 2
      Hash: ddae6b45ef41bc674a87fab7054e02f2
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: SplitToning
  - hash:
      serializedVersion: 2
      Hash: d497e277ee09923aa44c51901a8f68f9
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: LensFlareDataSRP
  - hash:
      serializedVersion: 2
      Hash: d0392b7f3f52d5af537ade186b4f51f1
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ControlTrack
  - hash:
      serializedVersion: 2
      Hash: 62f0721343832e059923424c47148cef
    assemblyName: Assembly-CSharp
    namespaceName: AudioSystem
    className: AudioEventChannel
  - hash:
      serializedVersion: 2
      Hash: 360d3bc2983b155d4ce0473cf344b4e5
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXUISliderBinder
  - hash:
      serializedVersion: 2
      Hash: 169eebe07197f3fc3dd4a1eb1548e555
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: CustomCursor
  - hash:
      serializedVersion: 2
      Hash: caa4cca1e2ed277451afeede9830b39e
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InvItemModelSwapper
  - hash:
      serializedVersion: 2
      Hash: a21c20230c4a86d58e4d89e480dd21fe
    assemblyName: Unity.RenderPipelines.Core.Samples.Runtime
    namespaceName: 
    className: SamplesShowcase
  - hash:
      serializedVersion: 2
      Hash: 4c4ed11ce6c2e3af0c94f356f8553b88
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ToolSelectionUILoader
  - hash:
      serializedVersion: 2
      Hash: e8e5776d4d5f8e94b4deaf56ec3869a3
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SettingsManager
  - hash:
      serializedVersion: 2
      Hash: 3645b97da6b9d55f246a57b0a5e2e8b7
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXLightBinder
  - hash:
      serializedVersion: 2
      Hash: 049f6aa84fe1cb5359c9338e96d0b07e
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: UIFoldout
  - hash:
      serializedVersion: 2
      Hash: 6226d014b62fa9fb55d7c2ed52e2fbd9
    assemblyName: Assembly-CSharp
    namespaceName: KinematicCharacterController.FPS
    className: FPSCharacterCamera
  - hash:
      serializedVersion: 2
      Hash: 95f3271f974b9017d9fd5679d75ca207
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: DiffusionProfileSettings
  - hash:
      serializedVersion: 2
      Hash: 8bbce9932f67ad8e7f4eefec87800f94
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextContainer
  - hash:
      serializedVersion: 2
      Hash: 6db45a47e58fc19f187c1fdc4b93f1a0
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: GrapplingHookSystem
  - hash:
      serializedVersion: 2
      Hash: 745c234e274606053ecac394bbce2be6
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXUIDropdownBinder
  - hash:
      serializedVersion: 2
      Hash: ad49e20f31ed572e586aa8bbaafb591b
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDropMessageListener
  - hash:
      serializedVersion: 2
      Hash: 9ebfe99653cb499c2cb6c5b7b2f6144b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: CubeLevelEditor
  - hash:
      serializedVersion: 2
      Hash: 6f4e984805a398651302813c0e84111f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: GrabInteraction
  - hash:
      serializedVersion: 2
      Hash: 04b6685b6f724c0d6024e6b6f96c8202
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: TimelineAsset
  - hash:
      serializedVersion: 2
      Hash: 6d394de691f6d0187f61c08889353d0e
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnButtonClickMessageListener
  - hash:
      serializedVersion: 2
      Hash: b98001682cba83786775c9e415b89a61
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDropdownValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: bb8d968ef4474e71aaad4a4dce279f90
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseDownMessageListener
  - hash:
      serializedVersion: 2
      Hash: 62e337a27cbe8ac548e34c50440f7fd8
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: WaterExcluder
  - hash:
      serializedVersion: 2
      Hash: 7e9cad7f71f9cc52f699bbd2d2a75734
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerClickMessageListener
  - hash:
      serializedVersion: 2
      Hash: 9fbeb15625dc9ac513505bbcb4b16bf2
    assemblyName: Assembly-CSharp
    namespaceName: KinematicCharacterController
    className: PhysicsMover
  - hash:
      serializedVersion: 2
      Hash: 0363343ddebc3a5b7b76284d9e6a39ea
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: Exposure
  - hash:
      serializedVersion: 2
      Hash: 5f70b16cd9acc9c1dd88b51b6ed89669
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIndirectToggle
  - hash:
      serializedVersion: 2
      Hash: 5874b789cd9832847c61ebfa803213af
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UIElements
    className: PanelEventHandler
  - hash:
      serializedVersion: 2
      Hash: cb49ce80d4b69cb2ed30d83bfcb146ae
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerBitField
  - hash:
      serializedVersion: 2
      Hash: d2ecd94550edf1b56aff07b1f4e41c57
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerEnter2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 887b515d9db4557998fd9c78913cf7f9
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: KinematicVaultSystem
  - hash:
      serializedVersion: 2
      Hash: 5ebc58ec194dd44630b25fcc270ad4af
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Dropdown
  - hash:
      serializedVersion: 2
      Hash: 92321bed02571e17a7ef031a33a83cfe
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: KinematicWallRun
  - hash:
      serializedVersion: 2
      Hash: 2cc5af0f17cef9c717df88c8c7e4c9aa
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine
    className: LightAnchor
  - hash:
      serializedVersion: 2
      Hash: 0a0017af174c0cfe8c861e881ad9dfb7
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: DynamicLightIntensity
  - hash:
      serializedVersion: 2
      Hash: b9b2354c4280703b18456e966be6c482
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: HDRISky
  - hash:
      serializedVersion: 2
      Hash: 60e6bd7baf2021d53f9dff6f7c923489
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InteractionManager
  - hash:
      serializedVersion: 2
      Hash: 9dfcc05cef04d72282ac90a933032536
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: BrutalistPlaygroundGenerator
  - hash:
      serializedVersion: 2
      Hash: 3f42e4313f313bb1c7dbd37cf976b0bc
    assemblyName: Tayx.Graphy
    namespaceName: Tayx.Graphy.Fps
    className: G_FpsGraph
  - hash:
      serializedVersion: 2
      Hash: 344eda256b1c252e0d5c75290ce069ac
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: PathTracing
  - hash:
      serializedVersion: 2
      Hash: 30535eaf4510ed3b6369a5c353127f22
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: GameFlowManager
  - hash:
      serializedVersion: 2
      Hash: 5dc51b0140815ee83ba613dce32f5b12
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerPersistentCanvas
  - hash:
      serializedVersion: 2
      Hash: 467801465e746f4b76688593fcbf8d06
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: KinematicPlatform
  - hash:
      serializedVersion: 2
      Hash: dbcefb58404831740c7ee663442e3156
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: ftLightmapsStorage
  - hash:
      serializedVersion: 2
      Hash: 1c96a47c7d3609f217a3575edb2cef90
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: BakeryLightMesh
  - hash:
      serializedVersion: 2
      Hash: 0491b92ea665977c768ddc84f83a08eb
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: BakeryVolume
  - hash:
      serializedVersion: 2
      Hash: b5007673bbbdb9d12597a1d8201370f5
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Text
  - hash:
      serializedVersion: 2
      Hash: a15e6b6c12054e0e91f3608ce991d588
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ShopUI
  - hash:
      serializedVersion: 2
      Hash: a69bac3c3ce5f4095117b1c4aa0ad94e
    assemblyName: Tayx.Graphy
    namespaceName: Graphy.Runtime.UI
    className: G_SafeArea
  - hash:
      serializedVersion: 2
      Hash: 920c5d759a5bbce31f086504949100f8
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ItemCreationTool
  - hash:
      serializedVersion: 2
      Hash: bc3544e4a0dbb5e6237782fe595a877a
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: LightCluster
  - hash:
      serializedVersion: 2
      Hash: a6d4760c1aaaf3fc6e618d120234abd0
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: SpawnOverDistance
  - hash:
      serializedVersion: 2
      Hash: d5cd7f207e91ca8336c65a577a6fb2a0
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ScrollRect
  - hash:
      serializedVersion: 2
      Hash: f45600bcc886206dd55a3208474aad61
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollbarValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 00bbd9db8634496d079d17711b2ff7c4
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Selectable
  - hash:
      serializedVersion: 2
      Hash: fce9b835d1e8b08e7ecea8da19f21986
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: CoroutineRunner
  - hash:
      serializedVersion: 2
      Hash: 0f723e8457ba62c9101c2f7a507a3867
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: 
    className: VisualEffectActivationClip
  - hash:
      serializedVersion: 2
      Hash: ac258ab74aefc5f0855f314e21f97903
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ToolDefinition
  - hash:
      serializedVersion: 2
      Hash: 6217b1287eca7eed313cf0b864249a30
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionExit2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: fb79e95da8891fc7ac6c36ca6967af23
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ActivationTrack
  - hash:
      serializedVersion: 2
      Hash: 552dca338f20933ce362ebbe559a6aa6
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 2923652a6597bebbffc4771389958657
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SelectionColor
  - hash:
      serializedVersion: 2
      Hash: bf8a44f2b2e5453bca35db50c2031e08
    assemblyName: Unity.RenderPipelines.Core.Samples.Runtime
    namespaceName: UnityEngine.Rendering
    className: SamplesFreeCamera
  - hash:
      serializedVersion: 2
      Hash: 191cd500c627cd6682ef6f70b8e47f2c
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionEnter2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: b12b807aba617dce9638e80f7063d7a4
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: CurrencyManagerBootstrap
  - hash:
      serializedVersion: 2
      Hash: ef38c1407dba8c1f5d69f5db61a694fe
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: AnimatorMessageListener
  - hash:
      serializedVersion: 2
      Hash: c08408fe9ee79ab501659e765ec1b978
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: RayTracingSettings
  - hash:
      serializedVersion: 2
      Hash: 6fb4e2330918ced23e58c69c0a56b7b4
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: KinematicClimbingSystem
  - hash:
      serializedVersion: 2
      Hash: 1071e9f64164208bfb0cba042809d2aa
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: FlareGun
  - hash:
      serializedVersion: 2
      Hash: a022bc48e34d0c47d2e2b6782d30d550
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: BakerySector
  - hash:
      serializedVersion: 2
      Hash: b12640dfdb5966813e9dd838893e6c4a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8d5318ed8488b1fe17c08044ce1b8309
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_StyleSheet
  - hash:
      serializedVersion: 2
      Hash: e18dfd2b913b09fbbb2405165a2e6a44
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_Dropdown
  - hash:
      serializedVersion: 2
      Hash: ada873da50974d1e7ce247ffb296b0f3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 66e0f6ed90813bad2df9bbe85518ea31
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: BakeryAlwaysRender
  - hash:
      serializedVersion: 2
      Hash: 5382156409d7c462c67822a5ee8d6c85
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerToggleHistory
  - hash:
      serializedVersion: 2
      Hash: dcce92cb1e31f7a1cfc6b964a2c1126b
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: HDAdditionalReflectionData
  - hash:
      serializedVersion: 2
      Hash: 185e7bacfbbc5defefa609c70f7ff4ce
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_ColorGradient
  - hash:
      serializedVersion: 2
      Hash: 5b7c4cac4cbc6c8f95422d6a8cd0e36f
    assemblyName: Assembly-CSharp
    namespaceName: KinematicCharacterController
    className: KinematicCharacterSystem
  - hash:
      serializedVersion: 2
      Hash: e402a382f213255c22bda9254dd831b3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: SceneVariables
  - hash:
      serializedVersion: 2
      Hash: 364686f14c4dac051fa6e76a0fd5460e
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: VehicleInteractable
  - hash:
      serializedVersion: 2
      Hash: 540882762544409cc4530e60db00e951
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: CanvasScaler
  - hash:
      serializedVersion: 2
      Hash: ee39bc7a9d41c5e61daf56a0a079f969
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: BakeryPackAsSingleSquare
  - hash:
      serializedVersion: 2
      Hash: c6e913ae6a2593d14c1f9b5946af9e84
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: DynamicFogController
  - hash:
      serializedVersion: 2
      Hash: 17142b03663387290480c82303274c02
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: edfba2ccd6ca727be951f7b54c706be8
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InteractionUI
  - hash:
      serializedVersion: 2
      Hash: 56b095bf9d1ece0fa9030efb4a40f152
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: ScreenSpaceLensFlare
  - hash:
      serializedVersion: 2
      Hash: 386db9f7751e5e96c2a1d069090577ff
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: PlanarReflectionProbe
  - hash:
      serializedVersion: 2
      Hash: a664577449200efc6f0a75fc8ce46867
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: HeadBob
  - hash:
      serializedVersion: 2
      Hash: a1515dadbbbc6f48c079b4300003a0e8
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: FPSPlayerManager
  - hash:
      serializedVersion: 2
      Hash: 00eef92bf387da2c9009267123d9d674
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerExit2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: ed2c59b75f8f3cd8df4cd4387d965e71
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector2
  - hash:
      serializedVersion: 2
      Hash: 68a964a1f10e29fadb90eed17efd3ce3
    assemblyName: Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: RequiredSettingsSO_HDRP
  - hash:
      serializedVersion: 2
      Hash: 2541c03d117382d4ebbe0bb0775dd2dc
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerProgressBar
  - hash:
      serializedVersion: 2
      Hash: f33b95fe9aa5580c1ff792b8447cc04e
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXUIToggleBinder
  - hash:
      serializedVersion: 2
      Hash: 5c21f4846181ee6e245db1fcfb62fc0e
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerContainer
  - hash:
      serializedVersion: 2
      Hash: 84331c03802a1740266f8fff107f8429
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: ColorAdjustments
  - hash:
      serializedVersion: 2
      Hash: 1bae4789eddd43919ba26a68e79954aa
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: HeadBobDebug
  - hash:
      serializedVersion: 2
      Hash: ca7965796a6fc0e5baad4cb19f5b5ded
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnJointBreak2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: dd69b015096a2f0a46d5735209fa9467
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: PhysicallyBasedSky
  - hash:
      serializedVersion: 2
      Hash: e0009641604c853b7f035faba504a5ef
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: WaterDeformer
  - hash:
      serializedVersion: 2
      Hash: cde402e184a6ab530144506bc9d6a0af
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector4
  - hash:
      serializedVersion: 2
      Hash: f0122e6b53254bb3572d87affdb487b2
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: MotionBlur
  - hash:
      serializedVersion: 2
      Hash: 3619bbe901cd3d551eb5ea7cc6882b89
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXAudioSpectrumBinder
  - hash:
      serializedVersion: 2
      Hash: 0814f85034902f3734f70fbf99b24c5c
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXRaycastBinder
  - hash:
      serializedVersion: 2
      Hash: f778e23b6ff26b8de4721be0eb1fa240
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionStayMessageListener
  - hash:
      serializedVersion: 2
      Hash: a14e344c15284258f76f50efa88f67b7
    assemblyName: Tayx.Graphy
    namespaceName: Tayx.Graphy.Fps
    className: G_FpsText
  - hash:
      serializedVersion: 2
      Hash: 3878830189dcf988ec8963937143b58e
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEditor.Rendering.HighDefinition
    className: MaterialExternalReferences
  - hash:
      serializedVersion: 2
      Hash: 19c7094e1c1b151057c372890e57f46f
    assemblyName: Unity.ProBuilder
    namespaceName: UnityEngine.ProBuilder
    className: BezierShape
  - hash:
      serializedVersion: 2
      Hash: c5645a528754465b4f324a52bd1bc5d1
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: PersistenceManager
  - hash:
      serializedVersion: 2
      Hash: b747f5d40674055dd70e18552f1b0447
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Mask
  - hash:
      serializedVersion: 2
      Hash: e6b258489c88376c5869946b2735e7e5
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_FontAsset
  - hash:
      serializedVersion: 2
      Hash: 6a0b5b415c00f783202fd1ae2d556cb2
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AudioPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 2aafdd015f21c8007f7526229b6646d1
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: StashUI
  - hash:
      serializedVersion: 2
      Hash: debd75e86f6ad9e505030da81f8ca441
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: VisualEffectControlTrack
  - hash:
      serializedVersion: 2
      Hash: a2992bb738fa3ff3046bffc43a6337b9
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Armor
  - hash:
      serializedVersion: 2
      Hash: c9320491348c38e97a9a37c69eca0b76
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ViewModelHeadBob
  - hash:
      serializedVersion: 2
      Hash: f812a0188f413717e8274e63c59aab25
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: UnityEngine.Rendering.HighDefinition
    className: LocalVolumetricFog
  - hash:
      serializedVersion: 2
      Hash: b88e78b6860b6061b2c681ea9b80b6bf
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerToggle
  - hash:
      serializedVersion: 2
      Hash: 600c7ee34c0f7163d1f1db99c4b69bbf
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: FPSControllerSettingsUI
  - hash:
      serializedVersion: 2
      Hash: db6133687572f9dc24f1fd4238ef9b83
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextMeshPro
  - hash:
      serializedVersion: 2
      Hash: 107991b46342fb8e48b5e632c374302f
    assemblyName: Unity.RenderPipelines.HighDefinition.Runtime
    namespaceName: 
    className: HDDynamicResolution
  - hash:
      serializedVersion: 2
      Hash: f55b45f1fd056dfeddab65334440172b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SpaceshipVehicle
  - hash:
      serializedVersion: 2
      Hash: 10c17b309bdca62b4d7fbaf113ed4ca0
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Toggle
  - hash:
      serializedVersion: 2
      Hash: 58f6c91dc9977217e7536afccdb74f71
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeAdjustmentVolume
  - hash:
      serializedVersion: 2
      Hash: 75a64e5c73e8796187bfb012035f803d
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXPreviousPositionBinder
  - hash:
      serializedVersion: 2
      Hash: 61268ab592453a893888fb08f8428c3f
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: LoopAndDelay
  - hash:
      serializedVersion: 2
      Hash: 9a174471d4db2c0c6d3ac47d2f818db1
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnParticleCollisionMessageListener
  - hash:
      serializedVersion: 2
      Hash: 9589f641d7c23662266625bfee5872e5
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Flare
  - hash:
      serializedVersion: 2
      Hash: 83cfda82082c233d96cc677c19f57e07
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnJointBreakMessageListener
  - hash:
      serializedVersion: 2
      Hash: 32f2869687bce2192ef2ff2f3b96a0ce
    assemblyName: Unity.ProBuilder
    namespaceName: UnityEngine.ProBuilder.Shapes
    className: ProBuilderShape
  - hash:
      serializedVersion: 2
      Hash: 4c485ce86c8bcce4ddbf52ad32d16887
    assemblyName: Assembly-CSharp
    namespaceName: KinematicCharacterController.FPS
    className: SpaceshipCameraController
  - hash:
      serializedVersion: 2
      Hash: a1ac4c50e55874de16eee3db71aa9784
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ToggleGroup
  - hash:
      serializedVersion: 2
      Hash: 4718916586b3818eb69bf65df61f0f3a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionStay2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 480455ab70cc715478a1513f73a0b165
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: BakeryProjectSettings
  - hash:
      serializedVersion: 2
      Hash: d82086405f46de14829f6b4bcbdd8fc9
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBecameInvisibleMessageListener
  - hash:
      serializedVersion: 2
      Hash: ba331fc935a6a1327554a41b7ff164fe
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: VFXRuntimeResources
  - hash:
      serializedVersion: 2
      Hash: f2305da2400dfe6f069770a1a63f0f7c
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: PlayableTrack
  - hash:
      serializedVersion: 2
      Hash: ca8445d8cca5cb71b771aa39eaf2be6f
    assemblyName: Assembly-CSharp
    namespaceName: ToolSelection
    className: ExtendedToolDefinition
  - hash:
      serializedVersion: 2
      Hash: 701629e24efdfe6496a445b5a26e4e45
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: PlatformButtonController
  - hash:
      serializedVersion: 2
      Hash: 09b59adae7e5f5864ec10c197d068023
    assemblyName: Tayx.Graphy
    namespaceName: Tayx.Graphy.Audio
    className: G_AudioManager
  - hash:
      serializedVersion: 2
      Hash: a3d3b1fd0eefc94a08c2822d35f1daa9
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InteractiveWorldScreen
  - hash:
      serializedVersion: 2
      Hash: 00de3ae5bb8e9b7168816d45fbaa13bb
    assemblyName: BakeryRuntimeAssembly
    namespaceName: 
    className: BakeryLightmapGroupSelector
  - hash:
      serializedVersion: 2
      Hash: bcdf337a25c34edce598684c104dfd7b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ItemBreakageSystem
  - hash:
      serializedVersion: 2
      Hash: 951f846c69cc216d5e01b381efce8262
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: DynamicFogSystem
  platform: 19
  scenePathNames:
  - Assets/_Game/Scenes/StartMenu.unity
  - Assets/_Game/Scenes/Main.unity
  - Assets/_Game/Scenes/TestScene.unity
  playerPath: C:/Unity/Builds/NLAME/BLAME.exe
